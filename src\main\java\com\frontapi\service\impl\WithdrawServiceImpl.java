package com.frontapi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.frontapi.dto.WithdrawDTO;
import com.frontapi.entity.FrontUser;
import com.frontapi.entity.SysParams;
import com.frontapi.entity.WithdrawRecord;
import com.frontapi.exception.BusinessException;
import com.frontapi.mapper.*;
import com.frontapi.service.WithdrawService;
import com.frontapi.service.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Service
@RequiredArgsConstructor
public class WithdrawServiceImpl implements WithdrawService {

    private final FrontUserMapper frontUserMapper;
    private final WithdrawRecordMapper withdrawRecordMapper;
    private final SysParamsMapper sysParamsMapper;
    private final UserService userService; // 新增：注入UserService
  

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createWithdraw(Long userId, WithdrawDTO withdrawDTO) {
        // 0. 校验支付密码
        userService.checkPayPassword(userId, withdrawDTO.getSecurityPassword());
        
        // 1. 获取系统参数
        SysParams params = sysParamsMapper.selectOne(new QueryWrapper<>());
        if (params == null) {
            throw new BusinessException("系统参数未配置");
        }

        // 2. 检查是否允许提现
        if (!params.getEnableWithdraw()) {
            throw new BusinessException("当前系统暂停提现功能");
        }

        // 3. 获取用户信息
        FrontUser user = frontUserMapper.selectById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }


        // 5. 校验提现金额
        BigDecimal amount = withdrawDTO.getAmount();
        // 检查最低提现金额
        if (amount.compareTo(params.getMinWithdraw()) < 0) {
            throw new BusinessException("最低提现金额为" + params.getMinWithdraw() + "USDT");
        }
        // 检查最高提现金额
        if (amount.compareTo(params.getMaxWithdraw()) > 0) {
            throw new BusinessException("最高提现金额为" + params.getMaxWithdraw() + "USDT");
        }

        // 检查余额是否充足
        if (amount.compareTo(user.getAvailableBalance()) > 0) {
            throw new BusinessException("可用余额不足");
        }

        // 6. 计算手续费和实际到账金额
        BigDecimal fee = params.getWithdrawFee(); // 固定手续费
        BigDecimal realAmount = amount.subtract(fee);

        // 7. 扣除用户余额（使用SQL命令，只修改相关字段）
        int updateResult = frontUserMapper.updateBalanceForWithdraw(user.getId(), amount);
        if (updateResult <= 0) {
            throw new RuntimeException("更新用户余额失败");
        }

        // 8. 创建提现记录
        WithdrawRecord record = new WithdrawRecord();
        record.setUserId(userId);
        record.setUsername(user.getUsername() == null ? user.getEmail() : user.getUsername());
        record.setRegisterEmail(user.getEmail());
        record.setAmount(amount);
        record.setFee(fee);
        record.setRealAmount(realAmount);
        record.setAddress(withdrawDTO.getAddress());
        record.setChainName(withdrawDTO.getChainName());
        record.setStatus(0); // 待审核
        record.setRemark(withdrawDTO.getRemark() != null ? withdrawDTO.getRemark() : "提现");
        // 使用 LocalDateTime
        LocalDateTime now = LocalDateTime.now();
        record.setCreateTime(now);
        record.setUpdateTime(now);
        withdrawRecordMapper.insert(record);

      //验证是否为自动处理提现（0为手动，1为自动）
//      if(params.getAutoWithdraw()){
//        //如果是自动提现就获取他的id
//        boolean result =  true;
//        if(result){
//          //当提现成功时修改状态以及备注
//          record.setStatus(1); // 已通过
//          record.setRemark("系统自动处理");
//          withdrawRecordMapper.updateById(record);
//          // 减少冻结余额
//          user.setFrozenBalance(user.getFrozenBalance().subtract(amount));
//          frontUserMapper.updateById(user);
//
//        }
//      }
    }

    

} 