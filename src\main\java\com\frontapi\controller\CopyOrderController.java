package com.frontapi.controller;

import com.frontapi.dto.ApiResponse;
import com.frontapi.dto.CreateOrderRequest;
import com.frontapi.entity.DeliveryOrder;
import com.frontapi.mapper.DeliveryOrderMapper;
import com.frontapi.service.DeliveryOrderService;
import com.frontapi.vo.CopyOrderVO;
import com.frontapi.vo.UserVO;
import com.frontapi.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/copy/order")
public class CopyOrderController {
    @Autowired
    private DeliveryOrderService deliveryOrderService;
    @Autowired
    private DeliveryOrderMapper deliveryOrderMapper;
    @Autowired
    private UserService userService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private RestTemplate restTemplate;

    @GetMapping("/list")
    public ApiResponse<Map<String, Object>> getOrderList(
            @RequestParam String type,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int pageSize
    ) {
        UserVO currentUser = userService.getCurrentUserInfo();
        if (currentUser == null) {
            return ApiResponse.error("用户未登录");
        }
        Long userId = currentUser.getId();
        List<CopyOrderVO> list;
        int total;
        if ("today".equals(type)) {
            list = deliveryOrderService.getTodayOrdersPaged(userId, page, pageSize);
            total = deliveryOrderService.countTodayOrders(userId);
        } else {
            list = deliveryOrderService.getHistoryOrdersPaged(userId, page, pageSize);
            total = deliveryOrderService.countHistoryOrders(userId);
        }
        Map<String, Object> data = new HashMap<>();
        data.put("list", list);
        data.put("total", total);
        return ApiResponse.success(data);
    }

    @GetMapping("/open-list")
    public ApiResponse<Map<String, Object>> getOpenOrderList(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int pageSize
    ) {
        UserVO currentUser = userService.getCurrentUserInfo();
        if (currentUser == null) {
            return ApiResponse.error("用户未登录");
        }
        Long userId = currentUser.getId();
        List<CopyOrderVO> list = deliveryOrderService.getOpenOrders(userId, page, pageSize);
        int total = deliveryOrderService.countOpenOrders(userId);
        Map<String, Object> data = new HashMap<>();
        data.put("list", list);
        data.put("total", total);
        return ApiResponse.success(data);
    }

    @GetMapping("/profit-list")
    public ApiResponse<Map<String, Object>> getProfitOrderList(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int pageSize
    ) {
        UserVO currentUser = userService.getCurrentUserInfo();
        if (currentUser == null) {
            return ApiResponse.error("用户未登录");
        }
        Long userId = currentUser.getId();
        List<CopyOrderVO> list = deliveryOrderService.getProfitOrders(userId, page, pageSize);
        int total = deliveryOrderService.countProfitOrders(userId);
        Map<String, Object> data = new HashMap<>();
        data.put("list", list);
        data.put("total", total);
        return ApiResponse.success(data);
    }

    @GetMapping("/closed-list")
    public ApiResponse<Map<String, Object>> getClosedOrderList(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int pageSize
    ) {
        UserVO currentUser = userService.getCurrentUserInfo();
        if (currentUser == null) {
            return ApiResponse.error("用户未登录");
        }
        Long userId = currentUser.getId();
        List<CopyOrderVO> list = deliveryOrderService.getClosedOrders(userId, page, pageSize);
        int total = deliveryOrderService.countClosedOrders(userId);
        Map<String, Object> data = new HashMap<>();
        data.put("list", list);
        data.put("total", total);
        return ApiResponse.success(data);
    }

    @PostMapping("/create")
    public ApiResponse<String> createOrder(@RequestBody CreateOrderRequest request) {
        try {
            // 1. 验证用户登录
            UserVO currentUser = userService.getCurrentUserInfo();
            if (currentUser == null) {
                return ApiResponse.error("用户未登录");
            }

            // 2. 验证是否为带单员
            if (currentUser.getIsLeader() == null || currentUser.getIsLeader() != 1) {
                return ApiResponse.error("只有带单员才能下单");
            }

            // 3. 带单员下单前验证
            if (!validateLeaderCanCreateOrder(currentUser.getId())) {
                return ApiResponse.error("当前无法下单，请先处理未完成的订单");
            }

            // 4. 验证请求参数
            if (request.getSymbol() == null || request.getSymbol().trim().isEmpty()) {
                return ApiResponse.error("交易对不能为空");
            }
            if (request.getDirection() == null || (request.getDirection() != 1 && request.getDirection() != 2)) {
                return ApiResponse.error("交易方向参数错误，1为买涨，2为买跌");
            }
            if (request.getLeverage() == null || (request.getLeverage() != 50 && request.getLeverage() != 100)) {
                return ApiResponse.error("杠杆倍数只能是50或100");
            }
            if (request.getQuantity() == null || request.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                return ApiResponse.error("购买数量必须大于0");
            }
            if (request.getTakeProfit() == null || request.getTakeProfit().compareTo(BigDecimal.ZERO) <= 0) {
                return ApiResponse.error("止盈价格不能为空且必须大于0");
            }
            if (request.getStopLoss() == null || request.getStopLoss().compareTo(BigDecimal.ZERO) <= 0) {
                return ApiResponse.error("止损价格不能为空且必须大于0");
            }

            // 5. 获取实时价格
            BigDecimal currentPrice = getCurrentPrice(request.getSymbol());
            if (currentPrice == null || currentPrice.compareTo(BigDecimal.ZERO) <= 0) {
                return ApiResponse.error("获取实时价格失败，请稍后重试");
            }
            // 格式化价格到8位小数
            currentPrice = formatPrice(currentPrice);

            // 6. 验证止盈止损价格合理性
            if (request.getDirection() == 1) { // 买涨
                if (request.getTakeProfit().compareTo(currentPrice) <= 0) {
                    return ApiResponse.error("买涨时止盈价格必须大于当前价格");
                }
                if (request.getStopLoss().compareTo(currentPrice) >= 0) {
                    return ApiResponse.error("买涨时止损价格必须小于当前价格");
                }
            } else { // 买跌
                if (request.getTakeProfit().compareTo(currentPrice) >= 0) {
                    return ApiResponse.error("买跌时止盈价格必须小于当前价格");
                }
                if (request.getStopLoss().compareTo(currentPrice) <= 0) {
                    return ApiResponse.error("买跌时止损价格必须大于当前价格");
                }
            }

            // 7. 计算保证金
            BigDecimal marginAmount = calculateMargin(request.getQuantity(), currentPrice, request.getLeverage());

            // 8. 验证跟单账户余额
            if (currentUser.getCopyTradeBalance() == null ||
                currentUser.getCopyTradeBalance().compareTo(marginAmount) < 0) {
                return ApiResponse.error(String.format("账户余额不足，需要%.2fUSDT，当前余额%.2fUSDT",
                    marginAmount, currentUser.getCopyTradeBalance()));
            }

            // 9. 创建带单订单
            DeliveryOrder order = new DeliveryOrder();
            order.setUserId(currentUser.getId());
            order.setLeaderId(currentUser.getId()); // 带单员自己就是leader
            order.setSymbol(request.getSymbol());
            order.setMarginAmount(marginAmount);
            order.setPositionAmount(request.getQuantity());
            order.setLever(request.getLeverage());
            order.setDirection(request.getDirection());
            order.setTakeProfit(formatPrice(request.getTakeProfit()));
            order.setStopLoss(formatPrice(request.getStopLoss()));
            order.setOpenPrice(currentPrice);
            order.setOpenTime(new Date());
            order.setStatus(0); // 0:开仓处理中，分佣完成后改为1
            order.setProfit(BigDecimal.ZERO);
            order.setProfitStatus(0); // 0:持仓中（未确定盈亏）
            order.setRebateStatus(1); // 1:未返 
            order.setIsSettlement(0); // 0:未结算
            order.setCreateTime(new Date());
            order.setUpdateTime(new Date());

            // 10. 保存带单员下单前的原始余额
            BigDecimal leaderOriginalBalance = currentUser.getCopyTradeBalance();

            // 11. 扣除保证金并创建订单
            boolean success = deliveryOrderService.createOrderWithBalance(order, marginAmount);
            if (!success) {
                return ApiResponse.error("下单失败，余额不足或系统异常");
            }

            // 12. 为跟单用户创建订单
            try {
                log.info("跟单计算参数 - 带单员ID: {}, 原始余额: {}, 保证金: {}, 订单数量: {}, 开仓价格: {}",
                    currentUser.getId(), leaderOriginalBalance, marginAmount,
                    order.getPositionAmount(), order.getOpenPrice());
                deliveryOrderService.createFollowOrders(order, currentPrice, leaderOriginalBalance);
                //
                log.info("带单员ID: {} 下单成功，已触发跟单逻辑，原始余额: {}", currentUser.getId(), leaderOriginalBalance);
            } catch (Exception e) {
                log.error("创建跟单订单失败，但带单员订单已成功", e);
                // 跟单失败不影响带单员的订单，只记录日志
            }

            return ApiResponse.success("下单成功");

        } catch (Exception e) {
            log.error("下单异常", e);
            return ApiResponse.error("下单失败：" + e.getMessage());
        }
    }

    /**
     * 获取持仓订单
     */
    @GetMapping("/hold")
    public ApiResponse<Map<String, Object>> getHoldOrders(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        try {
            UserVO currentUser = userService.getCurrentUserInfo();
            if (currentUser == null) {
                return ApiResponse.error("用户未登录");
            }

            List<CopyOrderVO> orders = deliveryOrderService.getOpenOrders(currentUser.getId(), page, pageSize);
            int total = deliveryOrderService.countOpenOrders(currentUser.getId());
            boolean hasMore = page * pageSize < total;

            Map<String, Object> result = new HashMap<>();
            result.put("list", orders);
            result.put("total", total);
            result.put("hasMore", hasMore);
            result.put("page", page);
            result.put("pageSize", pageSize);

            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("获取持仓订单失败", e);
            return ApiResponse.error("获取持仓订单失败");
        }
    }

    /**
     * 获取成交订单
     */
    @GetMapping("/deal")
    public ApiResponse<Map<String, Object>> getDealOrders(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        try {
            UserVO currentUser = userService.getCurrentUserInfo();
            if (currentUser == null) {
                return ApiResponse.error("用户未登录");
            }

            List<CopyOrderVO> orders = deliveryOrderService.getClosedOrders(currentUser.getId(), page, pageSize);
            int total = deliveryOrderService.countClosedOrders(currentUser.getId());
            boolean hasMore = page * pageSize < total;

            Map<String, Object> result = new HashMap<>();
            result.put("list", orders);
            result.put("total", total);
            result.put("hasMore", hasMore);
            result.put("page", page);
            result.put("pageSize", pageSize);

            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("获取成交订单失败", e);
            return ApiResponse.error("获取成交订单失败");
        }
    }

    /**
     * 获取盈利订单
     */
    @GetMapping("/profit")
    public ApiResponse<Map<String, Object>> getProfitOrders(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        try {
            UserVO currentUser = userService.getCurrentUserInfo();
            if (currentUser == null) {
                return ApiResponse.error("用户未登录");
            }

            List<CopyOrderVO> orders = deliveryOrderService.getProfitOrders(currentUser.getId(), page, pageSize);
            int total = deliveryOrderService.countProfitOrders(currentUser.getId());
            boolean hasMore = page * pageSize < total;

            Map<String, Object> result = new HashMap<>();
            result.put("list", orders);
            result.put("total", total);
            result.put("hasMore", hasMore);
            result.put("page", page);
            result.put("pageSize", pageSize);

            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("获取盈利订单失败", e);
            return ApiResponse.error("获取盈利订单失败");
        }
    }

    /**
     * 获取实时价格（带重试机制）
     */
    private BigDecimal getCurrentPrice(String symbol) {
        int maxRetries = 3; // 总共尝试3次（第一次 + 重试2次）

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                // 处理symbol格式：BTC/USDT -> BTCUSDT
                String redisKey = "binance:ticker:" + symbol.replace("/", "");
                log.info("第{}次尝试获取{}的实时价格，Redis键: {}", attempt, symbol, redisKey);

                String tickerJson = stringRedisTemplate.opsForValue().get(redisKey);
                if (tickerJson != null) {
                    JSONObject jsonObj = new JSONObject(tickerJson);
                    String lastPrice = jsonObj.getString("lastPrice");
                    if (lastPrice != null && !lastPrice.trim().isEmpty()) {
                        BigDecimal currentPrice = new BigDecimal(lastPrice).setScale(8, RoundingMode.HALF_UP);
                        log.info("第{}次尝试成功，获取{}实时价格: {}", attempt, symbol, currentPrice);
                        return currentPrice;
                    }
                }

                log.warn("第{}次尝试失败，Redis中未找到{}的有效价格数据", attempt, symbol);

                // 如果不是最后一次尝试，等待一小段时间再重试
                if (attempt < maxRetries) {
                    try {
                        Thread.sleep(100); // 等待100毫秒
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }

            } catch (Exception e) {
                log.error("第{}次尝试获取{}价格时发生异常", attempt, symbol, e);

                // 如果不是最后一次尝试，等待一小段时间再重试
                if (attempt < maxRetries) {
                    try {
                        Thread.sleep(100); // 等待100毫秒
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }

        // 3次尝试都失败，尝试从币安API获取
        log.warn("Redis获取{}价格失败，尝试币安API备用方案", symbol);
        return getPriceFromBinanceAPI(symbol);
    }

    /**
     * 直接从币安API获取价格（备用方案）
     */
    private BigDecimal getPriceFromBinanceAPI(String symbol) {
        try {
            // 处理symbol格式：BTC/USDT -> BTCUSDT
            String binanceSymbol = symbol.replace("/", "");
            String url = "https://api.binance.com/api/v3/ticker/price?symbol=" + binanceSymbol;

            log.info("从币安API获取{}价格，URL: {}", symbol, url);

            String response = restTemplate.getForObject(url, String.class);
            if (response != null) {
                JSONObject jsonObj = new JSONObject(response);
                String price = jsonObj.getString("price");
                if (price != null && !price.trim().isEmpty()) {
                    BigDecimal currentPrice = new BigDecimal(price).setScale(8, RoundingMode.HALF_UP);
                    log.info("从币安API成功获取{}价格: {}", symbol, currentPrice);
                    return currentPrice;
                }
            }

            log.warn("从币安API获取{}价格失败，响应为空或无效", symbol);
            return null;

        } catch (Exception e) {
            log.error("从币安API获取{}价格异常", symbol, e);
            return null;
        }
    }

    /**
     * 计算保证金：数量 * 价格 / 杠杆倍数
     */
    private BigDecimal calculateMargin(BigDecimal quantity, BigDecimal price, Integer leverage) {
        return quantity.multiply(price).divide(new BigDecimal(leverage), 8, RoundingMode.HALF_UP);
    }

    /**
     * 格式化价格到8位小数
     */
    private BigDecimal formatPrice(BigDecimal price) {
        if (price == null) {
            return null;
        }
        return price.setScale(8, RoundingMode.HALF_UP);
    }

    /**
     * 验证带单员是否可以下单
     * @param leaderId 带单员ID
     * @return true-可以下单，false-不能下单
     */
    private boolean validateLeaderCanCreateOrder(Long leaderId) {
        try {
            // 1. 检查是否有未平仓订单
            int unClosedCount = deliveryOrderMapper.countUnClosedOrdersByLeaderId(leaderId);
            if (unClosedCount > 0) {
                log.warn("带单员ID: {} 存在 {} 个未平仓订单，无法下单", leaderId, unClosedCount);
                return false;
            }

            // 2. 检查是否有未结算或结算中的订单
            int unSettledCount = deliveryOrderMapper.countUnSettledOrdersByLeaderId(leaderId);
            if (unSettledCount > 0) {
                log.warn("带单员ID: {} 存在 {} 个未结算或结算中订单，无法下单", leaderId, unSettledCount);
                return false;
            }

            // 3. 检查是否有亏损未返现的订单
            int lossUnRebateCount = deliveryOrderMapper.countLossUnRebateOrdersByLeaderId(leaderId);
            if (lossUnRebateCount > 0) {
                log.warn("带单员ID: {} 存在 {} 个亏损未返现订单，无法下单", leaderId, lossUnRebateCount);
                return false;
            }

            log.info("带单员ID: {} 验证通过，可以下单", leaderId);
            return true;

        } catch (Exception e) {
            log.error("验证带单员下单权限失败，带单员ID: {}", leaderId, e);
            return false;
        }
    }

    /**
     * 一键平仓
     */
    @PostMapping("/close")
    public ApiResponse<String> closePosition(@RequestBody Map<String, Object> request) {
        try {
            UserVO currentUser = userService.getCurrentUserInfo();
            if (currentUser == null) {
                return ApiResponse.error("用户未登录");
            }

            // 验证是否为带单员
            if (currentUser.getIsLeader() == null || currentUser.getIsLeader() != 1) {
                return ApiResponse.error("只有带单员才能平仓");
            }

            Long orderId = Long.valueOf(request.get("orderId").toString());
            if (orderId == null) {
                return ApiResponse.error("订单ID不能为空");
            }

            // 执行平仓操作
            boolean success = deliveryOrderService.closePosition(orderId, currentUser.getId());
            if (success) {
                return ApiResponse.success("平仓成功");
            } else {
                return ApiResponse.error("平仓失败，订单不存在或已平仓");
            }

        } catch (Exception e) {
            log.error("平仓操作失败", e);
            return ApiResponse.error("平仓失败: " + e.getMessage());
        }
    }
}