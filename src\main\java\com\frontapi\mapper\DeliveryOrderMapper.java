package com.frontapi.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import java.math.BigDecimal;
import java.util.List;
import com.frontapi.vo.CopyOrderVO;

@Mapper
public interface DeliveryOrderMapper {
    @Select("SELECT COUNT(1) FROM delivery_order WHERE user_id = #{userId} AND status = 1")
    int countOpenOrderByUserId(@Param("userId") Long userId);

    @Select("SELECT IFNULL(SUM(profit),0) FROM delivery_order WHERE user_id = #{userId}")
    BigDecimal getTotalProfit(@Param("userId") Long userId);

    @Select("SELECT IFNULL(SUM(profit),0) FROM delivery_order WHERE user_id = #{userId} AND DATE(create_time) = CURDATE()")
    BigDecimal getTodayProfit(@Param("userId") Long userId);

    List<CopyOrderVO> selectTodayOrders(@Param("userId") Long userId);
    List<CopyOrderVO> selectHistoryOrders(@Param("userId") Long userId);
    List<CopyOrderVO> selectTodayOrdersPaged(@Param("userId") Long userId, @Param("offset") int offset, @Param("limit") int limit);
    int countTodayOrders(@Param("userId") Long userId);
    List<CopyOrderVO> selectHistoryOrdersPaged(@Param("userId") Long userId, @Param("offset") int offset, @Param("limit") int limit);
    int countHistoryOrders(@Param("userId") Long userId);
    // 查询持仓订单
    List<CopyOrderVO> selectOpenOrders(@Param("userId") Long userId, @Param("offset") int offset, @Param("limit") int limit);
    int countOpenOrders(@Param("userId") Long userId);
    // 查询盈利订单
    List<CopyOrderVO> selectProfitOrders(@Param("userId") Long userId, @Param("offset") int offset, @Param("limit") int limit);
    int countProfitOrders(@Param("userId") Long userId);
    // 查询所有已平仓订单
    List<CopyOrderVO> selectClosedOrders(@Param("userId") Long userId, @Param("offset") int offset, @Param("limit") int limit);
    int countClosedOrders(@Param("userId") Long userId);
    // 查询最近平仓的订单（用于实时推送）
    List<CopyOrderVO> selectRecentClosedOrders(@Param("userId") Long userId, @Param("limit") int limit);
    // 插入订单
    int insertOrder(com.frontapi.entity.DeliveryOrder order);

    // 更新订单
    int updateById(com.frontapi.entity.DeliveryOrder order);

    // 根据ID查询订单
    com.frontapi.entity.DeliveryOrder selectById(@Param("id") Long id);

    // 根据带单员订单ID查询所有跟单订单（用于平仓处理）
    List<com.frontapi.entity.DeliveryOrder> selectFollowOrdersByLeaderOrderId(@Param("leaderOrderId") Long leaderOrderId);

    // 根据带单员ID查询盈利且未返利的订单（用于返利状态更新）
    List<com.frontapi.entity.DeliveryOrder> selectProfitOrdersByLeaderId(@Param("leaderId") Long leaderId);

    // 更新订单结算状态
    int updateSettlementStatusByLeaderOrderId(@Param("leaderOrderId") Long leaderOrderId, @Param("settlementStatus") Integer settlementStatus);

    // 带单员下单前验证方法

    /**
     * 检查带单员是否有未平仓订单
     */
    @Select("SELECT COUNT(1) FROM delivery_order WHERE leader_id = #{leaderId} AND status = 1")
    int countUnClosedOrdersByLeaderId(@Param("leaderId") Long leaderId);

    /**
     * 检查带单员是否有未结算或结算中的订单
     */
    @Select("SELECT COUNT(1) FROM delivery_order WHERE leader_id = #{leaderId} AND is_settlement IN (0, 1)")
    int countUnSettledOrdersByLeaderId(@Param("leaderId") Long leaderId);

    /**
     * 检查带单员是否有亏损未返现的订单
     */
    @Select("SELECT COUNT(1) FROM delivery_order WHERE leader_id = #{leaderId} AND profit_status = 2 AND rebate_status = 1")
    int countLossUnRebateOrdersByLeaderId(@Param("leaderId") Long leaderId);

    /**
     * 查询所有持仓中且设置了止盈止损的订单（用于自动平仓监控）
     */
    @Select("SELECT * FROM delivery_order WHERE status = 1 AND (take_profit > 0 OR stop_loss > 0)")
    List<com.frontapi.entity.DeliveryOrder> selectOpenOrdersWithStopConditions();

    /**
     * 查询指定带单员的所有跟单订单
     */
    @Select("SELECT * FROM delivery_order WHERE leader_id = #{leaderId} AND symbol = #{symbol} AND status = 1 AND user_id != leader_id")
    List<com.frontapi.entity.DeliveryOrder> selectFollowerOrdersByLeader(@Param("leaderId") Long leaderId, @Param("symbol") String symbol);

    /**
     * 更新订单状态为开仓完成（从开仓处理中到持仓中）
     */
    @Update("UPDATE delivery_order SET status = 1 WHERE id = #{orderId} AND status = 0")
    int updateStatusToOpen(@Param("orderId") Long orderId);

    /**
     * 更新订单状态为平仓处理中（从持仓中到平仓处理中）
     */
    @Update("UPDATE delivery_order SET status = 3 WHERE id = #{orderId} AND status = 1")
    int updateStatusToClosing(@Param("orderId") Long orderId);

    /**
     * 更新订单状态为已平仓（从平仓处理中到已平仓）
     */
    @Update("UPDATE delivery_order SET status = 2, close_time = NOW() WHERE id = #{orderId} AND status = 3")
    int updateStatusToClosed(@Param("orderId") Long orderId);

    /**
     * 恢复订单状态为持仓中（平仓失败时使用）
     */
    @Update("UPDATE delivery_order SET status = 1 WHERE id = #{orderId} AND status = 3")
    int rollbackStatusToOpen(@Param("orderId") Long orderId);

    /**
     * 只更新订单结算状态（避免覆盖其他字段）
     */
    @Update("UPDATE delivery_order SET is_settlement = #{settlementStatus}, update_time = NOW() WHERE id = #{orderId}")
    int updateSettlementStatus(@Param("orderId") Long orderId, @Param("settlementStatus") Integer settlementStatus);

    /**
     * 同时更新订单结算状态和返佣状态（确保最终状态一致）
     */
    @Update("UPDATE delivery_order SET is_settlement = #{settlementStatus}, rebate_status = #{rebateStatus}, update_time = NOW() WHERE id = #{orderId}")
    int updateSettlementAndRebateStatus(@Param("orderId") Long orderId, @Param("settlementStatus") Integer settlementStatus, @Param("rebateStatus") Integer rebateStatus);

    /**
     * 根据带单员订单ID查询所有相关订单ID（包括带单员订单和跟单订单）
     * 只查询返佣状态不为2且结算状态不等于2的订单
     */
    @Select("SELECT id FROM delivery_order WHERE " +
            "((id = #{leaderOrderId}) OR " +
            "(leader_id = (SELECT leader_id FROM delivery_order WHERE id = #{leaderOrderId}) " +
            "AND DATE(open_time) = DATE((SELECT open_time FROM delivery_order WHERE id = #{leaderOrderId})) " +
            "AND symbol = (SELECT symbol FROM delivery_order WHERE id = #{leaderOrderId}))) " +
            "AND rebate_status != 2 " +
            "AND is_settlement != 2")
    List<Long> getRelatedOrderIds(@Param("leaderOrderId") Long leaderOrderId);

    /**
     * 批量更新订单的返佣状态
     */
    @Update("<script>" +
            "UPDATE delivery_order SET rebate_status = #{rebateStatus}, update_time = NOW() " +
            "WHERE id IN " +
            "<foreach collection='orderIds' item='orderId' open='(' separator=',' close=')'>" +
            "#{orderId}" +
            "</foreach>" +
            "</script>")
    int batchUpdateRebateStatus(@Param("orderIds") List<Long> orderIds, @Param("rebateStatus") Integer rebateStatus);

    /**
     * 更新单个订单的返佣状态
     */
    @Update("UPDATE delivery_order SET rebate_status = #{rebateStatus}, update_time = NOW() WHERE id = #{orderId}")
    int updateRebateStatus(@Param("orderId") Long orderId, @Param("rebateStatus") Integer rebateStatus);

    /**
     * 更新带单员盈利订单的返利状态为已返
     */
    @Update("UPDATE delivery_order SET rebate_status = 2, update_time = NOW() WHERE id = #{orderId} AND user_id = #{userId} AND leader_id = #{leaderId} AND profit_status = 1")
    int updateLeaderProfitOrderRebateStatus(@Param("orderId") Long orderId, @Param("userId") Long userId, @Param("leaderId") Long leaderId);

    /**
     * 查询带单员的订单，用于返佣状态更新
     */
    @Select("SELECT * FROM delivery_order WHERE " +
            "leader_id = #{leaderId} " +
            "AND user_id = #{leaderId} " +
            "AND symbol = #{symbol} " +
            "AND rebate_status != 2 " +
            "AND is_settlement != 2")
    List<com.frontapi.entity.DeliveryOrder> selectLeaderOrdersForRebateUpdate(
            @Param("leaderId") Long leaderId,
            @Param("symbol") String symbol);

    /**
     * 批量更新相关订单的结算状态为已结算（根据带单员订单ID）
     * 用于平仓完成后将所有相关订单标记为已结算
     */
    @Update("UPDATE delivery_order SET is_settlement = 2, update_time = NOW() WHERE " +
            "id IN (" +
            "  SELECT DISTINCT d1.id FROM (" +
            "    SELECT d.id FROM delivery_order d " +
            "    INNER JOIN (" +
            "      SELECT leader_id, symbol, open_time " +
            "      FROM delivery_order " +
            "      WHERE id = #{leaderOrderId}" +
            "    ) leader ON d.leader_id = leader.leader_id " +
            "      AND d.symbol = leader.symbol " +
            "      AND ABS(TIMESTAMPDIFF(SECOND, d.open_time, leader.open_time)) <= 60 " +
            "    WHERE d.status = 2" +
            "  ) d1" +
            ")")
    int batchUpdateRelatedOrdersSettlementStatus(@Param("leaderOrderId") Long leaderOrderId);
}