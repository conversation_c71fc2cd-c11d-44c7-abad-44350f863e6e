# 佣金分配方法优化说明

## 🎯 **修改目标**

根据用户要求，对 `assignCommission` 方法进行以下调整：
1. **只分配佣金比例，不减掉自己的佣金比例**
2. **保持推荐关系不变**
3. **增加激活状态检查**：被分配用户的 `is_activated` 必须是 1 才可以分配

## 🔄 **修改对比**

### 修改前的问题
```java
// 4. 查询自己佣金比例
FrontUser self = userMapper.selectById(currentUser.getId());
if (self.getCommissionRate() == null || self.getCommissionRate().compareTo(assignRate) < 0) {
    throw new RuntimeException("分配比例不能大于自己剩余佣金比例");
}

// 6. 更新自己佣金比例
int updateSelf = userMapper.updateCommissionRateById(self.getId(), self.getCommissionRate().subtract(assignRate));
if (updateSelf == 0) throw new RuntimeException("分配失败，自己的佣金比例已变更");
```

**问题**：
- ❌ 分配后会减掉自己的佣金比例
- ❌ 没有检查双方的激活状态
- ❌ 校验逻辑不够完善

### 修改后的优化
```java
// 2. 校验当前用户激活状态
FrontUser currentUserEntity = userMapper.selectById(currentUser.getId());
if (currentUserEntity.getIsActivated() == null || !currentUserEntity.getIsActivated()) {
    throw new RuntimeException("您未激活，无权限分配佣金比例");
}

// 7. 校验分配比例不能超过当前用户的佣金比例
if (currentUserEntity.getCommissionRate() == null ||
    assignRate.compareTo(currentUserEntity.getCommissionRate()) > 0) {
    throw new RuntimeException("分配比例不能超过您的佣金比例: " +
        (currentUserEntity.getCommissionRate() != null ? currentUserEntity.getCommissionRate() + "%" : "0%"));
}

// 8. 更新被分配用户佣金比例（不再减掉自己的佣金比例）
int updateTarget = userMapper.updateCommissionRateByUserNo(userNo, assignRate, BigDecimal.ZERO);
```

**优势**：
- ✅ 增加了双重激活状态检查
- ✅ 保持分配人佣金比例不变
- ✅ 增加了分配比例上限校验
- ✅ 增加了详细的日志记录

## 📋 **完整的校验流程**

### 1. **基础校验**
```java
// 1. 查询被分配用户
FrontUser targetUser = userMapper.selectByUserNo(userNo);
if (targetUser == null) throw new RuntimeException("被分配用户不存在");
```

### 2. **当前用户激活状态校验** ⭐ **新增**
```java
// 2. 校验当前用户激活状态
FrontUser currentUserEntity = userMapper.selectById(currentUser.getId());
if (currentUserEntity.getIsActivated() == null || !currentUserEntity.getIsActivated()) {
    throw new RuntimeException("您未激活，无权限分配佣金比例");
}
```

### 3. **被分配用户激活状态校验** ⭐ **新增**
```java
// 3. 校验被分配用户激活状态
if (targetUser.getIsActivated() == null || !targetUser.getIsActivated()) {
    throw new RuntimeException("该用户未激活，无权限分配佣金比例");
}
```

### 4. **推荐关系校验**
```java
// 4. 校验直推关系
if (!currentUser.getShareCode().equals(targetUser.getReferrerCode())) {
    throw new RuntimeException("只能分配给自己的直推成员");
}
```

### 5. **重复分配校验**
```java
// 5. 校验被分配用户佣金比例为0
if (targetUser.getCommissionRate() != null && targetUser.getCommissionRate().compareTo(BigDecimal.ZERO) > 0) {
    throw new RuntimeException("该用户已分配佣金比例，不能再次分配");
}
```

### 6. **分配比例基础校验** ⭐ **增强**
```java
// 6. 校验分配比例的有效性
if (assignRate == null || assignRate.compareTo(BigDecimal.ZERO) <= 0) {
    throw new RuntimeException("分配比例必须大于0");
}

if (assignRate.compareTo(new BigDecimal("100")) > 0) {
    throw new RuntimeException("分配比例不能超过100%");
}
```

### 7. **分配比例上限校验** ⭐ **新增**
```java
// 7. 校验分配比例不能超过当前用户的佣金比例
if (currentUserEntity.getCommissionRate() == null ||
    assignRate.compareTo(currentUserEntity.getCommissionRate()) > 0) {
    throw new RuntimeException("分配比例不能超过您的佣金比例: " +
        (currentUserEntity.getCommissionRate() != null ? currentUserEntity.getCommissionRate() + "%" : "0%"));
}
```

### 8. **执行分配** ⭐ **简化**
```java
// 8. 更新被分配用户佣金比例（不再减掉自己的佣金比例）
int updateTarget = userMapper.updateCommissionRateByUserNo(userNo, assignRate, BigDecimal.ZERO);
if (updateTarget == 0) throw new RuntimeException("分配失败，用户状态已变更");

log.info("佣金比例分配成功 - 分配人: {}(佣金比例:{}%), 被分配人: {}, 分配比例: {}%",
        currentUser.getUsername(), currentUserEntity.getCommissionRate(),
        targetUser.getUsername(), assignRate);
```

## 🔍 **数据库字段说明**

### FrontUser 表相关字段
```sql
-- 激活状态字段
`is_activated` tinyint(4) DEFAULT '0' COMMENT '是否激活(0:未激活,1:已激活)'

-- 佣金比例字段
`commission_rate` decimal(10,2) DEFAULT '0.00' COMMENT '佣金比例(%)'

-- 推荐关系字段
`share_code` varchar(20) NOT NULL COMMENT '分享码'
`referrer_code` varchar(20) DEFAULT NULL COMMENT '推荐人分享码'
```

### 激活状态检查逻辑
```java
// 检查激活状态的三种情况
if (targetUser.getIsActivated() == null ||     // 字段为null
    !targetUser.getIsActivated()) {            // 字段为false (0)
    throw new RuntimeException("该用户未激活，无权限分配佣金比例");
}

// 只有 is_activated = true (1) 才能通过检查
```

## 💡 **业务逻辑变化**

### 修改前的分配模式
```
分配人佣金比例：30%
被分配人佣金比例：0%
分配比例：20%

执行分配后：
分配人佣金比例：30% - 20% = 10%  ← 会减少
被分配人佣金比例：0% + 20% = 20%
```

### 修改后的分配模式
```
分配人佣金比例：30%
被分配人佣金比例：0%
分配比例：20% (≤ 30% ✅)

执行分配后：
分配人佣金比例：30%  ← 保持不变
被分配人佣金比例：0% + 20% = 20%
```

### 分配比例限制示例
```
✅ 正常情况：
分配人佣金比例：30%，分配比例：20% → 允许分配（20% ≤ 30%）

✅ 边界情况：
分配人佣金比例：30%，分配比例：30% → 允许分配（30% = 30%）

❌ 超限情况：
分配人佣金比例：30%，分配比例：35% → 拒绝分配（35% > 30%）
错误信息："分配比例不能超过您的佣金比例: 30%"

❌ 无佣金比例：
分配人佣金比例：null/0%，分配比例：10% → 拒绝分配
错误信息："分配比例不能超过您的佣金比例: 0%"
```

## 🎯 **级差佣金分配的配合**

这个修改与我们之前实现的级差佣金分配机制完美配合：

### 级差分配示例
假设推荐链：
```
用户A（下单用户）
  ↑ 推荐关系
用户B（佣金比例20%，有一键跟单）
  ↑ 推荐关系  
用户C（佣金比例30%，有一键跟单）
```

**级差计算**：
- 用户B获得：20% - 0% = 20%
- 用户C获得：30% - 20% = 10%

**分配优势**：
- 分配人可以给多个直推成员分配佣金比例
- 不会影响自己的佣金比例
- 通过级差机制，上级仍然能获得合理的佣金差额

## ✅ **修改总结**

### 主要变化
1. ✅ **移除了自己佣金比例的扣减逻辑**
2. ✅ **增加了双重激活状态检查**（当前用户 + 被分配用户）
3. ✅ **增强了分配比例的校验**
4. ✅ **简化了分配流程**
5. ✅ **增加了详细的日志记录**

### 保持不变
1. ✅ **推荐关系校验**：只能分配给直推成员
2. ✅ **重复分配校验**：已分配佣金比例的用户不能再次分配
3. ✅ **事务处理**：使用 `@Transactional` 确保数据一致性

### 新增功能
1. ✅ **双重激活状态校验**：分配人和被分配人都必须 `is_activated = 1`
2. ✅ **比例上限校验**：分配比例不能超过100%
3. ✅ **操作日志**：记录分配成功的详细信息

## 🧪 **测试建议**

### 测试场景
1. **正常分配**：已激活用户给已激活的直推成员分配佣金比例（分配比例 ≤ 自己的佣金比例）
2. **分配人未激活**：未激活用户尝试分配佣金比例（应该失败）
3. **被分配人未激活**：尝试给未激活用户分配（应该失败）
4. **双方都未激活**：未激活用户给未激活用户分配（应该失败）
5. **非直推关系**：尝试给非直推成员分配（应该失败）
6. **重复分配**：给已有佣金比例的用户再次分配（应该失败）
7. **无效比例**：分配0%或超过100%的比例（应该失败）
8. **超出自己佣金比例**：分配比例 > 自己的佣金比例（应该失败）⭐ **新增**
9. **边界值测试**：分配比例 = 自己的佣金比例（应该成功）⭐ **新增**
10. **无佣金比例分配**：自己佣金比例为0%或null时尝试分配（应该失败）⭐ **新增**

### 验证要点
- 分配成功后，分配人的佣金比例不变
- 被分配人的佣金比例正确更新
- 分配比例不能超过分配人的佣金比例
- 双方激活状态校验正确执行
- 级差佣金分配机制正常工作
- 所有校验规则正确执行
- 错误信息清晰明确，包含具体的佣金比例信息
