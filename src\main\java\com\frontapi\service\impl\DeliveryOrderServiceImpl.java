package com.frontapi.service.impl;

import com.frontapi.entity.FrontUser;
import com.frontapi.mapper.DeliveryOrderMapper;
import com.frontapi.mapper.FrontUserMapper;
import com.frontapi.mapper.TradeRecordMapper;
import com.frontapi.service.DeliveryOrderService;
import com.frontapi.service.FollowRelationCleanupService;
import com.frontapi.vo.CopyOrderVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;
import java.util.List;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class DeliveryOrderServiceImpl implements DeliveryOrderService {
    @Autowired
    private DeliveryOrderMapper deliveryOrderMapper;
    @Autowired
    private FrontUserMapper frontUserMapper;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private SettlementServiceImpl settlementService;
    @Autowired
    private TradeRecordMapper tradeRecordMapper;
    @Autowired
    private FollowRelationCleanupService followRelationCleanupService;

    @Override
    public BigDecimal getTodayProfit(Long userId) {
        return deliveryOrderMapper.getTodayProfit(userId);
    }

    @Override
    public BigDecimal getTotalProfit(Long userId) {
        return deliveryOrderMapper.getTotalProfit(userId);
    }

    @Override
    public List<CopyOrderVO> getTodayOrders(Long userId) {
        return deliveryOrderMapper.selectTodayOrders(userId);
    }

    @Override
    public List<CopyOrderVO> getHistoryOrders(Long userId) {
        return deliveryOrderMapper.selectHistoryOrders(userId);
    }

    @Override
    public List<CopyOrderVO> getTodayOrdersPaged(Long userId, int page, int pageSize) {
        int offset = (page - 1) * pageSize;
        return deliveryOrderMapper.selectTodayOrdersPaged(userId, offset, pageSize);
    }
    @Override
    public int countTodayOrders(Long userId) {
        return deliveryOrderMapper.countTodayOrders(userId);
    }
    @Override
    public List<CopyOrderVO> getHistoryOrdersPaged(Long userId, int page, int pageSize) {
        int offset = (page - 1) * pageSize;
        return deliveryOrderMapper.selectHistoryOrdersPaged(userId, offset, pageSize);
    }
    @Override
    public int countHistoryOrders(Long userId) {
        return deliveryOrderMapper.countHistoryOrders(userId);
    }

    @Override
    public List<CopyOrderVO> getOpenOrders(Long userId, int page, int pageSize) {
        int offset = (page - 1) * pageSize;
        return deliveryOrderMapper.selectOpenOrders(userId, offset, pageSize);
    }
    @Override
    public int countOpenOrders(Long userId) {
        return deliveryOrderMapper.countOpenOrders(userId);
    }
    @Override
    public List<CopyOrderVO> getProfitOrders(Long userId, int page, int pageSize) {
        int offset = (page - 1) * pageSize;
        return deliveryOrderMapper.selectProfitOrders(userId, offset, pageSize);
    }
    @Override
    public int countProfitOrders(Long userId) {
        return deliveryOrderMapper.countProfitOrders(userId);
    }
    @Override
    public List<CopyOrderVO> getClosedOrders(Long userId, int page, int pageSize) {
        int offset = (page - 1) * pageSize;
        return deliveryOrderMapper.selectClosedOrders(userId, offset, pageSize);
    }
    @Override
    public int countClosedOrders(Long userId) {
        return deliveryOrderMapper.countClosedOrders(userId);
    }
    @Override
    public void createOrder(com.frontapi.entity.DeliveryOrder order) {
        // 设置订单初始状态为开仓处理中
        order.setStatus(0);
        deliveryOrderMapper.insertOrder(order);

        // 异步执行开仓分润逻辑
        processOpenCommissionAsync(order.getId());
         
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createOrderWithBalance(com.frontapi.entity.DeliveryOrder order, BigDecimal marginAmount) {
        try {
            // 1. 检查用户余额
            BigDecimal currentBalance = frontUserMapper.getCopyTradeBalanceById(order.getUserId());
            if (currentBalance == null || currentBalance.compareTo(marginAmount) < 0) {
                throw new RuntimeException("跟单账户余额不足，当前余额: " +
                    (currentBalance != null ? currentBalance : "0") + "，需要保证金: " + marginAmount);
            }

            // 2. 扣除跟单账户余额
            int updateResult = frontUserMapper.decreaseCopyTradeBalance(order.getUserId(), marginAmount);
            if (updateResult <= 0) {
                throw new RuntimeException("扣除保证金失败，可能余额不足或账户异常");
            }

            // 3. 创建订单（设置为开仓处理中状态）
            order.setStatus(0);
            int insertResult = deliveryOrderMapper.insertOrder(order);
            if (insertResult <= 0) {
                throw new RuntimeException("订单创建失败，请检查订单参数或联系客服");
            }

            // 4. 记录交易明细（订单创建成功后记录）
            addTradeRecord(order.getUserId(), marginAmount.negate(), "下单扣除保证金",
                    "订单" + order.getId() + "扣除保证金", 2);

            // 5. 处理开仓手续费扣除和佣金分配
            settlementService.processOpenCommissionDistribution(order);

            log.info("用户{}下单成功，扣除保证金: {}, 订单ID: {}",
                    order.getUserId(), marginAmount, order.getId());
            return true;

        } catch (RuntimeException e) {
            // 重新抛出业务异常，保持错误信息
            throw e;
        } catch (Exception e) {
            log.error("创建订单异常，用户ID: {}, 保证金: {}", order.getUserId(), marginAmount, e);
            throw new RuntimeException("系统异常，下单失败: " + e.getMessage(), e);
        }
    }

    /**
     * 记录交易明细
     */
    private void addTradeRecord(Long userId, BigDecimal amount, String tradeType, String remark, int accountType) {
        try {
            // 获取用户信息
            String username = frontUserMapper.getUsernameById(userId);
            if (username == null) {
                username = "用户" + userId;
            }

            // 创建交易记录
            com.frontapi.entity.TradeRecord record = new com.frontapi.entity.TradeRecord();
            record.setUserId(userId);
            record.setUsername(username);
            record.setTradeType(tradeType);
            record.setAmount(amount);
            record.setAccountType(accountType);
            record.setRemark(remark);
            record.setCreateTime(java.time.LocalDateTime.now());
            record.setUpdateTime(java.time.LocalDateTime.now());

            int result = tradeRecordMapper.insert(record);
            if (result <= 0) {
                log.error("记录交易明细失败，用户ID: {}, 金额: {}", userId, amount);
            } else {
                log.info("记录交易明细成功，用户ID: {}, 类型: {}, 金额: {}", userId, tradeType, amount);
            }
        } catch (Exception e) {
            log.error("记录交易明细异常，用户ID: {}, 金额: {}", userId, amount, e);
            // 不抛出异常，避免影响主流程
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createFollowOrders(com.frontapi.entity.DeliveryOrder leaderOrder, BigDecimal currentPrice, BigDecimal leaderOriginalBalance) {
        try {
            // 1. 查询所有跟随该带单员的用户
            List<FrontUser> followers = frontUserMapper.selectFollowersByLeaderId(leaderOrder.getLeaderId());

            if (followers.isEmpty()) {
                log.info("带单员ID: {} 没有跟单用户", leaderOrder.getLeaderId());
                return;
            }

            log.info("为带单员ID: {} 创建 {} 个跟单订单", leaderOrder.getLeaderId(), followers.size());

            // 2. 为每个跟单用户创建订单
            for (FrontUser follower : followers) {
                try {
                    // 按比例计算跟单用户的购买数量
                    BigDecimal followerQuantity = calculateFollowerQuantityByRatio(
                        leaderOrder.getPositionAmount(),  // 带单员数量
                        leaderOrder.getOpenPrice(),       // 带单员开仓价格
                        follower.getCopyTradeBalance(),   // 跟单用户余额
                        leaderOriginalBalance,            // 带单员下单前的原始余额
                        currentPrice                      // 当前实时价格
                    );

                    // 根据数量计算实际保证金（使用带单员开仓价格保证一致性）
                    BigDecimal followerMargin = calculateMargin(followerQuantity, leaderOrder.getOpenPrice(), leaderOrder.getLever());

                    // 验证跟单用户余额是否充足承担相同风险比例
                    if (follower.getCopyTradeBalance() == null ||
                        follower.getCopyTradeBalance().compareTo(followerMargin) < 0) {
                        log.warn("跟单用户ID: {} 余额不足以承担相同风险比例，需要保证金: {}, 当前余额: {}, 跳过该用户",
                            follower.getId(), followerMargin, follower.getCopyTradeBalance());
                        continue; // 跳过余额不足的用户
                    }

                    // 创建跟单订单
                    com.frontapi.entity.DeliveryOrder followOrder = createFollowOrder(
                        leaderOrder, follower, followerQuantity, followerMargin, currentPrice);

                    // 扣除余额并创建订单
                    boolean success = createOrderWithBalance(followOrder, followerMargin);
                    if (success) {
                        log.info("为用户ID: {} 创建跟单订单成功，数量: {}, 保证金: {}",
                            follower.getId(), followerQuantity, followerMargin);
                    } else {
                        log.error("为用户ID: {} 创建跟单订单失败", follower.getId());
                    }

                } catch (Exception e) {
                    log.error("为用户ID: {} 创建跟单订单异常", follower.getId(), e);
                    // 继续处理下一个用户，不影响其他用户的跟单
                }
            }

        } catch (RuntimeException e) {
            // 重新抛出业务异常，保持错误信息
            throw e;
        } catch (Exception e) {
            log.error("批量创建跟单订单失败", e);
            throw new RuntimeException("跟单订单创建失败: " + e.getMessage(), e);
        }
    }

    /**
     * 按比例计算跟单用户的购买数量
     * 根据带单员的投入比例计算跟单用户的跟单金额和数量
     * 修改：跟单用户承担与带单员相同的风险比例，不限制投入比例为100%
     */
    private BigDecimal calculateFollowerQuantityByRatio(
            BigDecimal leaderQuantity,       // 带单员数量
            BigDecimal leaderOpenPrice,      // 带单员开仓价格
            BigDecimal followerBalance,      // 跟单用户余额
            BigDecimal leaderOriginalBalance, // 带单员下单前的原始余额
            BigDecimal currentPrice          // 当前实时价格
    ) {
        // 1. 计算带单员订单总价值
        BigDecimal leaderOrderValue = leaderQuantity.multiply(leaderOpenPrice);

        // 2. 计算带单员实际投入比例：带单员订单总价值 ÷ 带单员原始余额（不限制为100%）
        BigDecimal leaderInvestRatio = leaderOrderValue.divide(leaderOriginalBalance, 8, RoundingMode.HALF_UP);

        // 3. 跟单用户按相同风险比例投入：跟单用户余额 × 带单员实际投入比例
        BigDecimal followAmount = followerBalance.multiply(leaderInvestRatio);

        // 4. 根据跟单金额计算跟单数量：跟单金额 ÷ 带单员开仓价格（保证同样的开仓价格）
        BigDecimal followerQuantity = followAmount.divide(leaderOpenPrice, 8, RoundingMode.HALF_UP);

        log.info("跟单计算 - 带单员数量: {}, 开仓价格: {}, 订单价值: {}, 原始余额: {}, 实际投入比例: {}%, 跟单余额: {}, 跟单金额: {}, 跟单数量: {}",
            leaderQuantity, leaderOpenPrice, leaderOrderValue, leaderOriginalBalance,
            leaderInvestRatio.multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP),
            followerBalance, followAmount, followerQuantity);

        return followerQuantity;
    }

    /**
     * 计算保证金：数量 × 价格 ÷ 杠杆倍数
     */
    private BigDecimal calculateMargin(BigDecimal quantity, BigDecimal price, Integer leverage) {
        return quantity.multiply(price).divide(new BigDecimal(leverage), 8, RoundingMode.HALF_UP);
    }

    /**
     * 创建跟单订单对象
     */
    private com.frontapi.entity.DeliveryOrder createFollowOrder(
            com.frontapi.entity.DeliveryOrder leaderOrder,
            FrontUser follower,
            BigDecimal followerQuantity,
            BigDecimal followerMargin,
            BigDecimal followerOpenPrice) {

        com.frontapi.entity.DeliveryOrder followOrder = new com.frontapi.entity.DeliveryOrder();

        // 跟单用户信息
        followOrder.setUserId(follower.getId());
        followOrder.setLeaderId(leaderOrder.getLeaderId());

        // 交易参数（与带单员保持一致）
        followOrder.setSymbol(leaderOrder.getSymbol());           // 交易对

        followOrder.setDirection(leaderOrder.getDirection());     // 买涨买跌方向
        followOrder.setLever(leaderOrder.getLever());             // 杠杆倍数
        followOrder.setTakeProfit(formatPrice(leaderOrder.getTakeProfit()));   // 止盈价格
        followOrder.setStopLoss(formatPrice(leaderOrder.getStopLoss()));       // 止损价格

        // 跟单用户的开仓价格使用计算数量时的实时价格
        followOrder.setOpenPrice(formatPrice(followerOpenPrice));

        // 跟单用户特有的参数（根据其余额计算）
        followOrder.setMarginAmount(followerMargin);
        followOrder.setPositionAmount(followerQuantity);

        // 订单状态
        followOrder.setOpenTime(new Date());
        followOrder.setStatus(0); // 0:开仓处理中，分佣完成后改为1
        followOrder.setProfit(BigDecimal.ZERO);
        followOrder.setProfitStatus(0); // 0:持仓中（未确定盈亏）
        followOrder.setRebateStatus(1); // 1:未返
        followOrder.setIsSettlement(0); // 0:未结算
        followOrder.setCreateTime(new Date());
        followOrder.setUpdateTime(new Date());

        return followOrder;
    }

    @Override
    public List<com.frontapi.dto.CopyOrderProfitDTO> getUserHoldOrdersProfit(Long userId) {
        // 获取用户所有持仓订单
        List<CopyOrderVO> allOrders = deliveryOrderMapper.selectOpenOrders(userId, 0, 1000);
        // 获取最近5分钟内平仓的订单，用于推送移除通知
        List<CopyOrderVO> recentClosedOrders = deliveryOrderMapper.selectRecentClosedOrders(userId, 100);

        List<com.frontapi.dto.CopyOrderProfitDTO> result = new ArrayList<>();

        // 处理持仓订单
        for (CopyOrderVO order : allOrders) {
            try {
                // 获取实时价格
                String symbol = order.getSymbol();
                String tickerJson = stringRedisTemplate.opsForValue().get("binance:ticker:" + symbol.replace("/", ""));
                if (tickerJson == null) continue;

                org.json.JSONObject jsonObj = new org.json.JSONObject(tickerJson);
                String lastPrice = jsonObj.getString("lastPrice");
                if (lastPrice == null) continue;

                BigDecimal currentPrice = new BigDecimal(lastPrice);
                BigDecimal openPrice = order.getOpenPrice();
                BigDecimal quantity = order.getPositionAmount();

                // 计算实时盈利
                BigDecimal profit;
                BigDecimal profitRate;

                if (order.getDirection() == 1) {
                    // 买涨：盈利 = (当前价格 - 开仓价格) × 数量
                    profit = currentPrice.subtract(openPrice).multiply(quantity);
                    profitRate = currentPrice.subtract(openPrice)
                        .divide(openPrice, 6, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal(100));
                } else {
                    // 买跌：盈利 = (开仓价格 - 当前价格) × 数量
                    profit = openPrice.subtract(currentPrice).multiply(quantity);
                    profitRate = openPrice.subtract(currentPrice)
                        .divide(openPrice, 6, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal(100));
                }

                com.frontapi.dto.CopyOrderProfitDTO dto = new com.frontapi.dto.CopyOrderProfitDTO(
                    order.getId(),
                    profit,
                    profitRate,
                    order.getStatus(),
                    new Date(),
                    currentPrice
                );

                result.add(dto);

            } catch (Exception e) {
                log.error("计算订单{}实时盈利失败", order.getId(), e);
            }
        }

        // 处理最近平仓的订单，推送移除通知
        for (CopyOrderVO closedOrder : recentClosedOrders) {
            try {
                com.frontapi.dto.CopyOrderProfitDTO dto = new com.frontapi.dto.CopyOrderProfitDTO(
                    closedOrder.getId(),
                    closedOrder.getProfit(),
                    BigDecimal.ZERO, // 平仓后收益率不重要
                    closedOrder.getStatus(), // status = 2 表示已平仓
                    new Date(),
                    closedOrder.getClosePrice()
                );

                result.add(dto);
//                log.info("推送平仓订单移除通知: 订单ID={}, 状态={}", closedOrder.getId(), closedOrder.getStatus());

            } catch (Exception e) {
                log.error("处理平仓订单{}失败", closedOrder.getId(), e);
            }
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processClosePosition(Long orderId, String closeReason) {
        try {
            // 1. 查询订单
            com.frontapi.entity.DeliveryOrder order = deliveryOrderMapper.selectById(orderId);
            if (order == null) {
                log.warn("订单不存在，订单ID: {}", orderId);
                return;
            }

            // 检查订单状态，只处理持仓中或平仓处理中的订单
            if (order.getStatus() != 1 && order.getStatus() != 3) {
                log.warn("订单状态异常，订单ID: {}, 当前状态: {}, 期望状态: 1(持仓中)或3(平仓处理中)",
                        orderId, order.getStatus());
                return;
            }

            // 2. 获取实时价格
            String symbol = order.getSymbol();
            log.info("开始获取{}的实时价格", symbol);

            String redisKey = "binance:ticker:" + symbol;
            log.info("Redis查询键: {}", redisKey);

            // 调试：检查Redis中的相关键
            try {
                java.util.Set<String> keys = stringRedisTemplate.keys("binance:ticker:*");
                log.info("Redis中现有的价格键数量: {}", keys != null ? keys.size() : 0);
                if (keys != null && keys.size() > 0) {
                    log.info("前5个价格键示例: {}", keys.stream().limit(5).collect(java.util.stream.Collectors.toList()));
                }
            } catch (Exception e) {
                log.warn("检查Redis键失败", e);
            }

            // 获取实时价格（带重试机制）
            BigDecimal currentPrice = getCurrentPriceWithRetry(symbol);
            Date closeTime = new Date();

            // 3. 处理订单平仓
            processOrderClose(order, currentPrice, closeTime, closeReason);

            // 3.1. 处理订单的资金结算
            if (order.getUserId().equals(order.getLeaderId())) {
                // 带单员订单
                settlementService.processLeaderOrderSettlement(order);
            } else {
                // 跟单员订单
                settlementService.processFollowOrderSettlement(order);
            }

            // 3.2. 如果是带单员订单且盈利，更新返利状态为"已返"
            if (order.getUserId().equals(order.getLeaderId()) &&
                order.getProfit() != null &&
                order.getProfit().compareTo(BigDecimal.ZERO) > 0 &&
                order.getProfitStatus() == 1) {

                // 使用SQL直接更新返利状态，避免覆盖其他字段
                int updateResult = deliveryOrderMapper.updateLeaderProfitOrderRebateStatus(
                    order.getId(), order.getUserId(), order.getLeaderId());
                if (updateResult > 0) {
                    log.info("更新带单员订单返利状态成功，订单ID: {}, 盈利: {}, 返利状态: 1→2",
                            order.getId(), order.getProfit());
                    // 更新内存中的对象状态，保持一致性
                    //结算
                    order.setRebateStatus(2);
                    order.setUpdateTime(new Date());
                } else {
                    log.error("更新带单员订单返利状态失败，订单ID: {}", order.getId());
                }
            }

            // 4. 如果是带单员订单，查询所有跟单订单
            // 4. 如果是带单员订单，处理跟单订单
            if (order.getUserId().equals(order.getLeaderId())) {
                List<com.frontapi.entity.DeliveryOrder> followOrders = deliveryOrderMapper.selectFollowOrdersByLeaderOrderId(orderId);
                log.info("找到{}个跟单订单需要处理", followOrders.size());

                // 5. 先为所有跟单订单保存平仓价格，再计算收益
                log.info("=== 第一步：为所有跟单订单保存平仓价格 ===");
                int processedCount = 0;
                for (com.frontapi.entity.DeliveryOrder followOrder : followOrders) {
                    if (followOrder.getStatus() == 1 || followOrder.getStatus() == 3) { // 处理持仓中或平仓处理中的订单
                        processedCount++;
                        try {
                            log.info("为第{}个跟单订单保存平仓价格，订单ID: {}, 用户ID: {}",
                                    processedCount, followOrder.getId(), followOrder.getUserId());

                            // 为每个跟单订单重新获取实时价格（Redis + API备用方案）
                            BigDecimal followClosePrice = getPriceWithFallback(symbol);
                            if (followClosePrice == null) {
                                log.warn("跟单订单{}获取价格失败，使用开仓价格", followOrder.getId());
                                followClosePrice = followOrder.getOpenPrice();
                            }

                            // 先只保存平仓价格和时间，不计算收益
                            followOrder.setClosePrice(formatPrice(followClosePrice));
                            followOrder.setCloseTime(closeTime);
                            followOrder.setStatus(2); // 已平仓
                            followOrder.setIsSettlement(0); // 暂时设为未结算，等计算收益后再更新
                            followOrder.setUpdateTime(new Date());

                            int updateResult = deliveryOrderMapper.updateById(followOrder);
                            if (updateResult <= 0) {
                                log.error("保存跟单订单平仓价格失败，订单ID: {}", followOrder.getId());
                                throw new RuntimeException("保存平仓价格失败，订单ID: " + followOrder.getId());
                            }

                            log.info("跟单订单平仓价格保存成功，订单ID: {}, 平仓价格: {}",
                                    followOrder.getId(), followClosePrice);

                        } catch (Exception e) {
                            log.error("保存跟单订单平仓价格失败，订单ID: {}, 用户ID: {}",
                                    followOrder.getId(), followOrder.getUserId(), e);
                            // 继续处理其他订单，不因为一个订单失败而中断整个流程
                        }
                    } else {
                        log.info("跳过非持仓状态的跟单订单，订单ID: {}, 状态: {}",
                                followOrder.getId(), followOrder.getStatus());
                    }
                }

                log.info("=== 第二步：计算所有跟单订单的收益并结算 ===");
                int successCount = 0;
                // 重新查询订单，获取最新的平仓价格
                List<com.frontapi.entity.DeliveryOrder> updatedFollowOrders = deliveryOrderMapper.selectFollowOrdersByLeaderOrderId(orderId);
                for (com.frontapi.entity.DeliveryOrder followOrder : updatedFollowOrders) {
                    if (followOrder.getStatus() == 2 && followOrder.getClosePrice() != null) { // 已平仓且有平仓价格
                        try {
                            log.info("计算跟单订单收益，订单ID: {}, 用户ID: {}, 平仓价格: {}",
                                    followOrder.getId(), followOrder.getUserId(), followOrder.getClosePrice());

                            // 使用订单中保存的平仓价格计算收益
                            calculateAndSaveOrderProfit(followOrder);

                            // 处理跟单订单的资金结算
                            settlementService.processFollowOrderSettlement(followOrder);

                            successCount++;
                            log.info("跟单订单收益计算和结算成功，订单ID: {}, 用户ID: {}, 盈利: {}",
                                    followOrder.getId(), followOrder.getUserId(), followOrder.getProfit());

                        } catch (Exception e) {
                            log.error("跟单订单收益计算失败，订单ID: {}, 用户ID: {}",
                                    followOrder.getId(), followOrder.getUserId(), e);
                            // 继续处理其他订单，不因为一个订单失败而中断整个流程
                        }
                    } else {
                        log.warn("跳过无效的跟单订单，订单ID: {}, 状态: {}, 平仓价格: {}",
                                followOrder.getId(), followOrder.getStatus(), followOrder.getClosePrice());
                    }
                }

                log.info("跟单订单处理完成，总数: {}, 需处理: {}, 成功: {}",
                        followOrders.size(), processedCount, successCount);

                // 6. 更新这个带单员的盈利跟单订单的返利状态为"已返"
                updateRebateStatusForProfitOrders(order.getLeaderId());

                // 7. 所有订单结算完成后，清除不满足条件的跟单关系
                try {
                    int cleanupCount = followRelationCleanupService.cleanupInvalidFollowRelationsAfterSettlement(order.getLeaderId());
                    if (cleanupCount > 0) {
                        log.info("所有订单结算完成后清除了 {} 个不满足条件的跟单关系，带单员ID: {}", cleanupCount, order.getLeaderId());
                    }
                } catch (Exception e) {
                    log.error("所有订单结算完成后清除跟单关系失败，带单员ID: {}", order.getLeaderId(), e);
                    // 不抛出异常，避免影响平仓流程
                }
            }

            log.info("平仓处理完成，订单ID: {}, 原因: {}", orderId, closeReason);

        } catch (RuntimeException e) {
            // 重新抛出业务异常，保持错误信息
            throw e;
        } catch (Exception e) {
            log.error("平仓处理失败，订单ID: {}", orderId, e);
            throw new RuntimeException("平仓处理失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean closePosition(Long orderId, Long userId) {
        try {
            // 1. 查询订单是否存在且属于该用户
            com.frontapi.entity.DeliveryOrder order = deliveryOrderMapper.selectById(orderId);
            if (order == null) {
                log.warn("订单不存在，订单ID: {}", orderId);
                return false;
            }

            // 验证订单所有权（带单员只能平仓自己的订单）
            if (!order.getLeaderId().equals(userId)) {
                log.warn("用户{}尝试平仓不属于自己的订单{}", userId, orderId);
                return false;
            }

            // 验证订单状态
            switch (order.getStatus()) {
                case 0:
                    log.warn("订单{}开仓处理中，无法平仓", orderId);
                    throw new RuntimeException("订单开仓处理中，请稍后再试");
                case 2:
                    log.warn("订单{}已平仓", orderId);
                    throw new RuntimeException("订单已平仓");
                case 3:
                    log.warn("订单{}平仓处理中", orderId);
                    throw new RuntimeException("订单平仓处理中，请稍后再试");
                case 1:
                    // 可以平仓，继续处理
                    break;
                default:
                    log.warn("订单{}状态异常: {}", orderId, order.getStatus());
                    throw new RuntimeException("订单状态异常");
            }

            // 使用防重复执行的平仓方法
            boolean success = executeClosePositionWithLock(orderId, "手动平仓");

            if (success) {
                log.info("带单员{}手动平仓订单{}成功", userId, orderId);

                // 如果是带单员订单，处理跟单订单
                if (order.getUserId().equals(order.getLeaderId())) {
                    processFollowerOrdersForManualClose(order);

                    // 一键平仓后清除不满足条件的跟单关系
                    try {
                        int cleanupCount = followRelationCleanupService.cleanupInvalidFollowRelations(order.getLeaderId());
                        if (cleanupCount > 0) {
                            log.info("一键平仓后清除了 {} 个不满足条件的跟单关系，带单员ID: {}", cleanupCount, order.getLeaderId());
                        }
                    } catch (Exception e) {
                        log.error("一键平仓后清除跟单关系失败，带单员ID: {}", order.getLeaderId(), e);
                    }
                }

                return true;
            } else {
                log.warn("订单{}平仓失败，可能已被其他操作处理", orderId);
                throw new RuntimeException("订单正在处理中或已被平仓，请刷新页面查看最新状态");
            }

        } catch (RuntimeException e) {
            // 重新抛出业务异常，保持错误信息
            throw e;
        } catch (Exception e) {
            log.error("平仓操作失败，订单ID: {}", orderId, e);
            throw new RuntimeException("平仓操作失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理单个订单的平仓
     */
    private void processOrderClose(com.frontapi.entity.DeliveryOrder order, BigDecimal closePrice, Date closeTime, String closeReason) {
        log.info("=== 开始计算订单盈利 ===");
        log.info("订单ID: {}, 用户ID: {}, 交易对: {}", order.getId(), order.getUserId(), order.getSymbol());
        log.info("订单方向: {} (1=买涨, 2=买跌)", order.getDirection());
        log.info("开仓价格: {}, 平仓价格: {}", order.getOpenPrice(), closePrice);
        log.info("持仓数量: {}", order.getPositionAmount());

        // 计算盈利
        BigDecimal profit;
        if (order.getDirection() == 1) {
            // 买涨：盈利 = (平仓价格 - 开仓价格) × 数量
            profit = closePrice.subtract(order.getOpenPrice()).multiply(order.getPositionAmount());
            log.info("买涨计算: ({} - {}) × {} = {}", closePrice, order.getOpenPrice(), order.getPositionAmount(), profit);
        } else {
            // 买跌：盈利 = (开仓价格 - 平仓价格) × 数量
            profit = order.getOpenPrice().subtract(closePrice).multiply(order.getPositionAmount());
            log.info("买跌计算: ({} - {}) × {} = {}", order.getOpenPrice(), closePrice, order.getPositionAmount(), profit);
        }

        // 确定盈利状态
        int profitStatus;
        if (profit.compareTo(BigDecimal.ZERO) > 0) {
            profitStatus = 1; // 盈利
            log.info("订单盈利: {}, 盈利状态: 1(盈利)", profit);
        } else if (profit.compareTo(BigDecimal.ZERO) < 0) {
            profitStatus = 2; // 亏损
            log.info("订单亏损: {}, 盈利状态: 2(亏损)", profit);
        } else {
            profitStatus = 3; // 持平
            log.info("订单持平: {}, 盈利状态: 3(持平)", profit);
        }

        // 更新订单信息
        order.setClosePrice(formatPrice(closePrice));
        order.setCloseTime(closeTime);
        order.setStatus(2); // 已平仓
        order.setProfit(profit);
        order.setProfitStatus(profitStatus);
        order.setIsSettlement(1); // 待结算
        order.setUpdateTime(new Date());

        // 保存订单
        int updateResult = deliveryOrderMapper.updateById(order);
        if (updateResult <= 0) {
            log.error("更新订单失败，订单ID: {}", order.getId());
            throw new RuntimeException("订单状态更新失败，订单ID: " + order.getId());
        }

        log.info("订单{}平仓完成，平仓价格: {}, 盈利: {}, 原因: {}",
                order.getId(), closePrice, profit, closeReason);
    }

    /**
     * 使用订单中保存的平仓价格计算并保存收益
     */
    private void calculateAndSaveOrderProfit(com.frontapi.entity.DeliveryOrder order) {
        log.info("=== 开始计算订单收益（使用保存的平仓价格） ===");
        log.info("订单ID: {}, 用户ID: {}, 交易对: {}", order.getId(), order.getUserId(), order.getSymbol());
        log.info("订单方向: {} (1=买涨, 2=买跌)", order.getDirection());
        log.info("开仓价格: {}, 平仓价格: {}", order.getOpenPrice(), order.getClosePrice());
        log.info("持仓数量: {}", order.getPositionAmount());

        // 计算盈利
        BigDecimal profit;
        if (order.getDirection() == 1) {
            // 买涨：盈利 = (平仓价格 - 开仓价格) × 数量
            profit = order.getClosePrice().subtract(order.getOpenPrice()).multiply(order.getPositionAmount());
            log.info("买涨计算: ({} - {}) × {} = {}", order.getClosePrice(), order.getOpenPrice(), order.getPositionAmount(), profit);
        } else {
            // 买跌：盈利 = (开仓价格 - 平仓价格) × 数量
            profit = order.getOpenPrice().subtract(order.getClosePrice()).multiply(order.getPositionAmount());
            log.info("买跌计算: ({} - {}) × {} = {}", order.getOpenPrice(), order.getClosePrice(), order.getPositionAmount(), profit);
        }

        // 确定盈利状态
        int profitStatus;
        if (profit.compareTo(BigDecimal.ZERO) > 0) {
            profitStatus = 1; // 盈利
            log.info("订单盈利: {}, 盈利状态: 1(盈利)", profit);
        } else if (profit.compareTo(BigDecimal.ZERO) < 0) {
            profitStatus = 2; // 亏损
            log.info("订单亏损: {}, 盈利状态: 2(亏损)", profit);
        } else {
            profitStatus = 3; // 持平
            log.info("订单持平: {}, 盈利状态: 3(持平)", profit);
        }

        // 更新订单的收益信息
        order.setProfit(profit);
        order.setProfitStatus(profitStatus);
        order.setIsSettlement(1); // 待结算
        order.setUpdateTime(new Date());

        // 保存订单
        int updateResult = deliveryOrderMapper.updateById(order);
        if (updateResult <= 0) {
            log.error("更新订单收益失败，订单ID: {}", order.getId());
            throw new RuntimeException("订单收益更新失败，订单ID: " + order.getId());
        }

        log.info("订单{}收益计算完成，盈利: {}, 盈利状态: {}",
                order.getId(), profit, profitStatus);
    }

    /**
     * 更新这个带单员的盈利订单的返利状态为"已返"
     */
    private void updateRebateStatusForProfitOrders(Long leaderId) {
        try {
            log.info("开始更新带单员{}的盈利订单返利状态", leaderId);

            // 查询这个带单员的盈利且未返利的订单（包括带单员自己和跟单员的订单）
            List<com.frontapi.entity.DeliveryOrder> profitOrders = deliveryOrderMapper.selectProfitOrdersByLeaderId(leaderId);
            log.info("查询到{}个符合条件的盈利订单", profitOrders.size());

            int updatedCount = 0;
            for (com.frontapi.entity.DeliveryOrder profitOrder : profitOrders) {
                log.info("更新订单ID: {}, 用户ID: {}, 盈利: {}, 返利状态: 1→2",
                        profitOrder.getId(), profitOrder.getUserId(), profitOrder.getProfit());

                // 更新返利状态为"已返" (2表示已返)
                profitOrder.setRebateStatus(2);
                profitOrder.setUpdateTime(new Date());

                int result = deliveryOrderMapper.updateById(profitOrder);
                if (result > 0) {
                    updatedCount++;
                    log.info("更新盈利订单返利状态成功，订单ID: {}", profitOrder.getId());
                } else {
                    log.error("更新盈利订单返利状态失败，订单ID: {}", profitOrder.getId());
                }
            }

            log.info("返利状态更新完成，带单员ID: {}, 更新盈利订单数量: {}",
                    leaderId, updatedCount);

        } catch (Exception e) {
            log.error("更新返利状态失败，带单员ID: {}", leaderId, e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 格式化价格到8位小数
     */
    private BigDecimal formatPrice(BigDecimal price) {
        if (price == null) {
            return null;
        }
        return price.setScale(8, RoundingMode.HALF_UP);
    }

    /**
     * 获取当前价格（带重试机制，重试2次后仍失败则抛出异常）
     */
    private BigDecimal getCurrentPriceWithRetry(String symbol) {
        int maxRetries = 3; // 总共尝试3次（第一次 + 重试2次）

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                // 处理symbol格式：BTC/USDT -> BTCUSDT
                String redisKey = "binance:ticker:" + symbol.replace("/", "");
                log.info("第{}次尝试获取{}的实时价格，Redis键: {}", attempt, symbol, redisKey);

                String tickerJson = stringRedisTemplate.opsForValue().get(redisKey);
                if (tickerJson != null) {
                    org.json.JSONObject jsonObj = new org.json.JSONObject(tickerJson);
                    String lastPrice = jsonObj.getString("lastPrice");
                    if (lastPrice != null && !lastPrice.trim().isEmpty()) {
                        BigDecimal currentPrice = formatPrice(new BigDecimal(lastPrice));
                        log.info("第{}次尝试成功，获取{}实时价格: {}", attempt, symbol, currentPrice);
                        return currentPrice;
                    }
                }

                log.warn("第{}次尝试失败，Redis中未找到{}的有效价格数据", attempt, symbol);

                // 如果不是最后一次尝试，等待一小段时间再重试
                if (attempt < maxRetries) {
                    try {
                        Thread.sleep(100); // 等待100毫秒
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }

            } catch (Exception e) {
                log.error("第{}次尝试获取{}价格时发生异常", attempt, symbol, e);

                // 如果不是最后一次尝试，等待一小段时间再重试
                if (attempt < maxRetries) {
                    try {
                        Thread.sleep(100); // 等待100毫秒
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }

        // 3次尝试都失败，抛出异常
        log.error("经过{}次尝试仍无法获取{}的实时价格", maxRetries, symbol);
        throw new RuntimeException("无法获取" + symbol + "的实时价格，请稍后重试或联系客服");
    }

    /**
     * 获取当前价格（不带默认值，与Controller保持一致）
     */
    private BigDecimal getCurrentPrice(String symbol) {
        try {
            // 处理symbol格式：BTC/USDT -> BTCUSDT
            String redisKey = "binance:ticker:" + symbol.replace("/", "");
            String tickerJson = stringRedisTemplate.opsForValue().get(redisKey);
            if (tickerJson != null) {
                org.json.JSONObject jsonObj = new org.json.JSONObject(tickerJson);
                String lastPrice = jsonObj.getString("lastPrice");
                if (lastPrice != null) {
                    return formatPrice(new BigDecimal(lastPrice));
                }
            }
        } catch (Exception e) {
            log.error("获取价格失败", e);
        }
        return null;
    }

    /**
     * 直接从币安API获取价格（备用方案）
     */
    private BigDecimal getPriceFromBinanceAPI(String symbol) {
        try {
            // 处理symbol格式：BTC/USDT -> BTCUSDT
            String binanceSymbol = symbol.replace("/", "");
            String url = "https://api.binance.com/api/v3/ticker/price?symbol=" + binanceSymbol;

            log.info("从币安API获取{}价格，URL: {}", symbol, url);

            // 设置超时时间为5秒
            restTemplate.getInterceptors().clear();

            String response = restTemplate.getForObject(url, String.class);
            if (response != null) {
                org.json.JSONObject jsonObj = new org.json.JSONObject(response);
                String price = jsonObj.getString("price");
                if (price != null && !price.trim().isEmpty()) {
                    BigDecimal currentPrice = formatPrice(new BigDecimal(price));
                    log.info("从币安API成功获取{}价格: {}", symbol, currentPrice);
                    return currentPrice;
                }
            }

            log.warn("从币安API获取{}价格失败，响应为空或无效", symbol);
            return null;

        } catch (Exception e) {
            log.error("从币安API获取{}价格异常", symbol, e);
            return null;
        }
    }

    /**
     * 获取价格的完整方案（Redis + API备用）
     */
    private BigDecimal getPriceWithFallback(String symbol) {
        // 1. 首先尝试从Redis获取
        try {
            BigDecimal redisPrice = getCurrentPriceWithRetry(symbol);
            if (redisPrice != null) {
                return redisPrice;
            }
        } catch (Exception e) {
            log.warn("从Redis获取{}价格失败，尝试API备用方案", symbol, e);
        }

        // 2. Redis失败，尝试从币安API获取
        try {
            BigDecimal apiPrice = getPriceFromBinanceAPI(symbol);
            if (apiPrice != null) {
                log.info("使用币安API备用方案成功获取{}价格: {}", symbol, apiPrice);
                return apiPrice;
            }
        } catch (Exception e) {
            log.error("从币安API获取{}价格也失败", symbol, e);
        }

        // 3. 都失败了，返回null
        log.error("所有价格获取方案都失败，symbol: {}", symbol);
        return null;
    }

    /**
     * 处理单个订单的平仓（不包含跟单订单处理）
     */
    private void processSingleOrderClose(Long orderId, String closeReason) {
        try {
            // 1. 查询订单
            com.frontapi.entity.DeliveryOrder order = deliveryOrderMapper.selectById(orderId);
            if (order == null) {
                log.warn("订单不存在，订单ID: {}", orderId);
                return;
            }

            // 检查订单状态，只处理持仓中或平仓处理中的订单
            if (order.getStatus() != 1 && order.getStatus() != 3) {
                log.warn("订单状态异常，订单ID: {}, 当前状态: {}, 期望状态: 1(持仓中)或3(平仓处理中)",
                        orderId, order.getStatus());
                return;
            }

            // 2. 获取实时价格
            String symbol = order.getSymbol();
            log.info("开始获取{}的实时价格", symbol);

            // 获取实时价格（Redis + API备用方案）
            BigDecimal currentPrice = getPriceWithFallback(symbol);
            if (currentPrice == null) {
                log.warn("所有价格获取方案都失败，使用开仓价格作为平仓价格，symbol: {}", symbol);
                currentPrice = order.getOpenPrice(); // 最后的备用方案
            } else {
                log.info("成功获取{}的实时价格: {}", symbol, currentPrice);
            }
            Date closeTime = new Date();

            // 3. 处理订单平仓
            processOrderClose(order, currentPrice, closeTime, closeReason);

            // 4. 处理订单的资金结算
            if (order.getUserId().equals(order.getLeaderId())) {
                // 带单员订单
                settlementService.processLeaderOrderSettlement(order);
            } else {
                // 跟单员订单
                settlementService.processFollowOrderSettlement(order);
            }

            log.info("单个订单平仓处理完成，订单ID: {}, 原因: {}", orderId, closeReason);

        } catch (RuntimeException e) {
            // 重新抛出业务异常，保持错误信息
            throw e;
        } catch (Exception e) {
            log.error("单个订单平仓处理失败，订单ID: {}", orderId, e);
            throw new RuntimeException("平仓处理失败: " + e.getMessage(), e);
        }
    }

    /**
     * 带锁的平仓执行方法（防重复执行）
     */
    private boolean executeClosePositionWithLock(Long orderId, String closeReason) {
        String closingKey = "closing:" + orderId;

        try {
            // 1. 设置 Redis 标记
            Boolean setResult = stringRedisTemplate.opsForValue()
                .setIfAbsent(closingKey, "1", Duration.ofSeconds(30));

            if (!Boolean.TRUE.equals(setResult)) {
                log.warn("订单{}平仓标记设置失败，可能正在处理", orderId);
                return false;
            }

            // 2. 原子更新订单状态
            int affectedRows = deliveryOrderMapper.updateStatusToClosing(orderId);

            if (affectedRows != 1) {
                // 状态更新失败，清理 Redis 标记
                stringRedisTemplate.delete(closingKey);
                log.warn("订单{}状态更新失败，可能已被其他操作处理", orderId);
                return false;
            }

            // 3. 执行平仓逻辑（包含状态更新）
            processSingleOrderClose(orderId, closeReason);


            log.info("订单{}平仓完成", orderId);
            return true;

        } catch (Exception e) {
            log.error("订单{}平仓执行失败", orderId, e);
            // 异常时恢复状态
            deliveryOrderMapper.rollbackStatusToOpen(orderId);
            return false;

        } finally {
            // 4. 修改本地跟单的和带单订单的状态
            try {
                // 批量更新所有相关订单的返利状态和结算状态（将未结算的订单更新为已返利和已结算）
                int updateCount = deliveryOrderMapper.batchUpdateRelatedOrdersRebateStatus(orderId);
                if (updateCount > 0) {
                    log.info("批量更新相关订单状态完成，带单员订单ID: {}, 更新订单数: {}, 状态: rebate_status=2, is_settlement=2（仅未结算订单）",
                            orderId, updateCount);
                }
            } catch (Exception e) {
                log.error("批量更新相关订单状态失败，带单员订单ID: {}", orderId, e);
            }

            // 5. 清理 Redis 标记
            stringRedisTemplate.delete(closingKey);
        }
    }

    /**
     * 处理手动平仓时的跟单订单
     */
    private void processFollowerOrdersForManualClose(com.frontapi.entity.DeliveryOrder leaderOrder) {
        try {
            // 查找所有跟单该带单员的持仓订单
            java.util.List<com.frontapi.entity.DeliveryOrder> followerOrders =
                deliveryOrderMapper.selectFollowerOrdersByLeader(
                    leaderOrder.getLeaderId(), leaderOrder.getSymbol());

            if (followerOrders == null || followerOrders.isEmpty()) {
                log.info("带单员订单{}没有跟单订单需要处理", leaderOrder.getId());
                return;
            }

            log.info("带单员订单{}手动平仓完成，开始处理 {} 个跟单订单",
                    leaderOrder.getId(), followerOrders.size());

            // 平仓所有跟单订单
            for (com.frontapi.entity.DeliveryOrder followerOrder : followerOrders) {
                try {
                    boolean success = executeClosePositionWithLock(followerOrder.getId(), "跟随平仓");
                    if (success) {
                        log.info("跟单订单平仓成功，订单ID: {}", followerOrder.getId());
                    } else {
                        log.warn("跟单订单平仓失败，订单ID: {}", followerOrder.getId());
                    }
                } catch (Exception e) {
                    log.error("跟单订单平仓异常，订单ID: {}", followerOrder.getId(), e);
                }
            }

            log.info("带单员订单{}的所有跟单订单处理完成", leaderOrder.getId());

        } catch (Exception e) {
            log.error("处理带单员{}的跟单订单失败", leaderOrder.getId(), e);
        }
    }

    /**
     * 异步处理开仓分润逻辑
     */
    private void processOpenCommissionAsync(Long orderId) {
        String openingKey = "opening:" + orderId;

        try {
            // 1. 设置开仓处理标记
            Boolean setResult = stringRedisTemplate.opsForValue()
                .setIfAbsent(openingKey, "1", Duration.ofSeconds(60));

            if (!Boolean.TRUE.equals(setResult)) {
                log.warn("订单{}开仓标记设置失败，可能正在处理", orderId);
                return;
            }

            // 2. 再次检查订单状态
            com.frontapi.entity.DeliveryOrder order = deliveryOrderMapper.selectById(orderId);
            if (order.getStatus() != 0) {
                stringRedisTemplate.delete(openingKey);
                log.warn("订单{}状态已变更为: {}, 跳过开仓分润", orderId, order.getStatus());
                return;
            }

            // 3. 执行开仓分润逻辑
            processOpenCommission(orderId);

            // 4. 更新订单状态为持仓中
            int affectedRows = deliveryOrderMapper.updateStatusToOpen(orderId);
            if (affectedRows == 1) {
                log.info("订单{}开仓分润完成，状态更新为持仓中", orderId);
            } else {
                log.warn("订单{}状态更新失败，当前状态可能已变更", orderId);
            }

        } catch (Exception e) {
            log.error("订单{}开仓分润处理失败", orderId, e);
            // 可以考虑删除订单或标记为异常状态
            handleOpenCommissionError(orderId);

        } finally {
            // 清理开仓标记
            stringRedisTemplate.delete(openingKey);
        }
    }

    /**
     * 执行开仓分润逻辑
     */
    private void processOpenCommission(Long orderId) {
        try {
            // 这里应该包含原有的开仓分润逻辑
            // 例如：扣除开仓手续费、分配佣金等
            log.info("执行订单{}的开仓分润逻辑", orderId);

            // 模拟开仓分润处理时间
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }

            // 实际的开仓分润逻辑应该在这里实现
            // settlementService.processOpenCommission(orderId);

        } catch (Exception e) {
            log.error("订单{}开仓分润逻辑执行失败", orderId, e);
            throw e;
        }
    }

    /**
     * 处理开仓分润错误
     */
    private void handleOpenCommissionError(Long orderId) {
        try {
            // 可以选择删除订单或标记为异常状态
            // 这里暂时保持订单状态为0，等待人工处理
            log.error("订单{}开仓分润失败，需要人工处理", orderId);

        } catch (Exception e) {
            log.error("处理订单{}开仓分润错误时发生异常", orderId, e);
        }
    }
}