package com.frontapi.service.impl;

import com.frontapi.entity.DeliveryOrder;
import com.frontapi.mapper.DeliveryOrderMapper;
import com.frontapi.service.AutoCloseService;
import com.frontapi.service.DeliveryOrderService;
import com.frontapi.service.FollowRelationCleanupService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 自动平仓服务实现类
 */
@Slf4j
@Service
public class AutoCloseServiceImpl implements AutoCloseService {

    @Autowired
    private DeliveryOrderMapper deliveryOrderMapper;

    @Autowired
    private DeliveryOrderService deliveryOrderService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private FollowRelationCleanupService followRelationCleanupService;

    @Value("${auto-close.check-interval:5}")
    private int checkInterval;

    @Value("${auto-close.thread-pool-size:2}")
    private int threadPoolSize;

    private ScheduledExecutorService scheduler;
    private volatile boolean isMonitoring = false;
    
    @Override
    public void startAutoCloseMonitoring() {
        if (isMonitoring) {
            log.info("自动平仓监控已在运行中");
            return;
        }
        
        scheduler = Executors.newScheduledThreadPool(threadPoolSize);
        isMonitoring = true;

        // 根据配置的间隔检查
        scheduler.scheduleWithFixedDelay(this::monitorOrders, 0, checkInterval, TimeUnit.SECONDS);

        log.info("自动平仓监控已启动，检查间隔: {}秒，线程池大小: {}", checkInterval, threadPoolSize);
    }
    
    @Override
    public void stopAutoCloseMonitoring() {
        if (!isMonitoring) {
            log.info("自动平仓监控未运行");
            return;
        }
        
        isMonitoring = false;
        if (scheduler != null) {
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(10, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        log.info("自动平仓监控已停止");
    }
    
    /**
     * 监控所有持仓订单
     */
    @Async
    private void monitorOrders() {
        try {
            // 查询所有持仓中且设置了止盈止损的订单
            List<DeliveryOrder> openOrders = deliveryOrderMapper.selectOpenOrdersWithStopConditions();
            
            if (openOrders == null || openOrders.isEmpty()) {
                return;
            }
            
            log.debug("检查 {} 个持仓订单的止盈止损条件", openOrders.size());
            
            for (DeliveryOrder order : openOrders) {
                try {
                    checkAndAutoClose(order);
                } catch (Exception e) {
                    log.error("检查订单自动平仓失败，订单ID: {}", order.getId(), e);
                }
            }
            
        } catch (Exception e) {
            log.error("监控订单自动平仓异常", e);
        }
    }
    
    /**
     * 检查并执行自动平仓
     */
    private void checkAndAutoClose(DeliveryOrder order) {
        try {
            // 1. 获取最新的订单状态（重要：不能使用查询时的旧数据）
            DeliveryOrder currentOrder = deliveryOrderMapper.selectById(order.getId());

            // 2. 检查当前状态是否允许平仓
            if (currentOrder.getStatus() != 1) {
                log.debug("订单{}当前状态不允许平仓，状态: {}", order.getId(), currentOrder.getStatus());
                return;
            }

            // 3. 获取当前价格
            BigDecimal currentPrice = getCurrentPriceFromRedis(currentOrder.getSymbol());
            if (currentPrice == null) {
                log.warn("无法获取交易对{}的当前价格，跳过检查，订单ID: {}", currentOrder.getSymbol(), currentOrder.getId());
                return;
            }

            // 4. 检查是否需要自动平仓
            if (shouldAutoClose(currentOrder, currentPrice)) {
                String reason = getAutoCloseReason(currentOrder, currentPrice);

                log.info("触发自动平仓条件，订单ID: {}, 交易对: {}, 当前价格: {}, 原因: {}",
                        currentOrder.getId(), currentOrder.getSymbol(), currentPrice, reason);

                // 5. 尝试执行自动平仓（包含实时状态检查和防重复机制）
                boolean success = executeClosePosition(currentOrder.getId(), reason);

                if (success) {
                    log.info("自动平仓成功，订单ID: {}, 原因: {}", currentOrder.getId(), reason);

                    // 如果是带单员订单，处理跟单订单
                    if (currentOrder.getUserId().equals(currentOrder.getLeaderId())) {
                        processFollowerOrders(currentOrder, reason);

                        // 注意：跟单关系清除已移到结算完成后执行，确保佣金分配完成
                        log.info("自动平仓完成，跟单关系清除将在结算完成后执行，带单员ID: {}", currentOrder.getLeaderId());
                    }
                }
            }

        } catch (Exception e) {
            log.error("检查订单自动平仓失败，订单ID: {}", order.getId(), e);
        }
    }

    /**
     * 从Redis获取当前价格
     * 使用与系统其他地方相同的价格获取方法
     */
    private BigDecimal getCurrentPriceFromRedis(String symbol) {
        try {
            String redisKey = "binance:ticker:" + symbol;
            String tickerJson = stringRedisTemplate.opsForValue().get(redisKey);
            if (tickerJson != null) {
                org.json.JSONObject jsonObj = new org.json.JSONObject(tickerJson);
                String lastPrice = jsonObj.getString("lastPrice");
                if (lastPrice != null && !lastPrice.trim().isEmpty()) {
                    return new BigDecimal(lastPrice).setScale(8, RoundingMode.HALF_UP);
                }
            }
        } catch (Exception e) {
            log.error("从Redis获取{}价格失败", symbol, e);
        }
        return null;
    }
    
    @Override
    public boolean shouldAutoClose(DeliveryOrder order, BigDecimal currentPrice) {
        if (order == null || currentPrice == null) {
            return false;
        }
        
        // 检查止盈条件
        if (order.getTakeProfit() != null && order.getTakeProfit().compareTo(BigDecimal.ZERO) > 0) {
            if (order.getDirection() == 1) { // 买涨
                if (currentPrice.compareTo(order.getTakeProfit()) >= 0) {
                    return true; // 当前价格 >= 止盈价格
                }
            } else { // 买跌
                if (currentPrice.compareTo(order.getTakeProfit()) <= 0) {
                    return true; // 当前价格 <= 止盈价格
                }
            }
        }
        
        // 检查止损条件
        if (order.getStopLoss() != null && order.getStopLoss().compareTo(BigDecimal.ZERO) > 0) {
            if (order.getDirection() == 1) { // 买涨
                if (currentPrice.compareTo(order.getStopLoss()) <= 0) {
                    return true; // 当前价格 <= 止损价格
                }
            } else { // 买跌
                if (currentPrice.compareTo(order.getStopLoss()) >= 0) {
                    return true; // 当前价格 >= 止损价格
                }
            }
        }
        
        return false;
    }
    
    @Override
    public String getAutoCloseReason(DeliveryOrder order, BigDecimal currentPrice) {
        if (order == null || currentPrice == null) {
            return "未知原因";
        }
        
        // 检查止盈条件
        if (order.getTakeProfit() != null && order.getTakeProfit().compareTo(BigDecimal.ZERO) > 0) {
            if (order.getDirection() == 1) { // 买涨
                if (currentPrice.compareTo(order.getTakeProfit()) >= 0) {
                    return "止盈平仓";
                }
            } else { // 买跌
                if (currentPrice.compareTo(order.getTakeProfit()) <= 0) {
                    return "止盈平仓";
                }
            }
        }
        
        // 检查止损条件
        if (order.getStopLoss() != null && order.getStopLoss().compareTo(BigDecimal.ZERO) > 0) {
            if (order.getDirection() == 1) { // 买涨
                if (currentPrice.compareTo(order.getStopLoss()) <= 0) {
                    return "止损平仓";
                }
            } else { // 买跌
                if (currentPrice.compareTo(order.getStopLoss()) >= 0) {
                    return "止损平仓";
                }
            }
        }
        
        return "自动平仓";
    }
    
    /**
     * 尝试开始平仓流程（防重复执行的核心方法）
     */
    private boolean tryStartCloseProcess(Long orderId) {
        String closingKey = "closing:" + orderId;

        try {
            // 1. 检查订单当前状态
            DeliveryOrder order = deliveryOrderMapper.selectById(orderId);
            if (order.getStatus() != 1) {
                log.debug("订单{}状态不是持仓中，当前状态: {}", orderId, order.getStatus());
                return false;
            }

            // 2. 设置 Redis 标记
            Boolean setResult = stringRedisTemplate.opsForValue()
                .setIfAbsent(closingKey, "1", java.time.Duration.ofSeconds(30));

            if (!Boolean.TRUE.equals(setResult)) {
                log.debug("订单{}平仓标记设置失败，可能正在处理", orderId);
                return false;
            }

            // 3. 原子更新订单状态（关键：基于当前状态更新）
            int affectedRows = deliveryOrderMapper.updateStatusToClosing(orderId);

            if (affectedRows != 1) {
                // 状态更新失败，清理 Redis 标记
                stringRedisTemplate.delete(closingKey);
                log.debug("订单{}状态更新失败，可能已被其他操作处理", orderId);
                return false;
            }

            return true;

        } catch (Exception e) {
            // 异常时清理 Redis 标记
            stringRedisTemplate.delete(closingKey);
            log.error("订单{}开始平仓流程失败", orderId, e);
            return false;
        }
    }

    /**
     * 执行平仓操作（完整的防重复执行流程）
     */
    public boolean executeClosePosition(Long orderId, String closeReason) {
        String closingKey = "closing:" + orderId;

        try {
            // 1. 尝试开始平仓流程
            if (!tryStartCloseProcess(orderId)) {
                return false;
            }

            // 2. 执行平仓逻辑
            deliveryOrderService.processClosePosition(orderId, closeReason);

            // 3. 更新最终状态
            deliveryOrderMapper.updateStatusToClosed(orderId);

            log.info("订单{}平仓完成", orderId);
            return true;

        } catch (Exception e) {
            log.error("订单{}平仓执行失败", orderId, e);
            // 异常时恢复状态
            deliveryOrderMapper.rollbackStatusToOpen(orderId);
            return false;

        } finally {
            // 4. 修改本地跟单的和带单订单的状态
            try {
                // 批量更新所有相关订单的结算状态为已结算（条件：已返利且未结算的订单）
                int updateCount = deliveryOrderMapper.batchUpdateRelatedOrdersSettlementStatus(orderId);
                if (updateCount > 0) {
                    log.info("自动平仓-批量更新相关订单结算状态完成，带单员订单ID: {}, 更新订单数: {}, 状态: is_settlement=2（条件：已返利且未结算）",
                            orderId, updateCount);
                }
            } catch (Exception e) {
                log.error("自动平仓-批量更新相关订单结算状态失败，带单员订单ID: {}", orderId, e);
            }

            // 5. 清理 Redis 标记
            stringRedisTemplate.delete(closingKey);
        }
    }
    
    /**
     * 处理跟单订单（带单员平仓后处理所有跟单订单）
     */
    private void processFollowerOrders(DeliveryOrder leaderOrder, String reason) {
        try {
            // 1. 查找所有跟单该带单员的持仓订单
            List<DeliveryOrder> followerOrders = deliveryOrderMapper.selectFollowerOrdersByLeader(
                    leaderOrder.getLeaderId(), leaderOrder.getSymbol());

            if (followerOrders == null || followerOrders.isEmpty()) {
                log.info("带单员订单{}没有跟单订单需要处理", leaderOrder.getId());
                return;
            }

            log.info("带单员订单{}平仓完成，开始处理 {} 个跟单订单",
                    leaderOrder.getId(), followerOrders.size());

            // 2. 平仓所有跟单订单
            for (DeliveryOrder followerOrder : followerOrders) {
                try {
                    boolean success = executeClosePosition(followerOrder.getId(), reason);
                    if (success) {
                        log.info("跟单订单自动平仓成功，订单ID: {}", followerOrder.getId());
                    } else {
                        log.warn("跟单订单自动平仓失败，订单ID: {}", followerOrder.getId());
                    }
                } catch (Exception e) {
                    log.error("跟单订单自动平仓异常，订单ID: {}", followerOrder.getId(), e);
                }
            }

            log.info("带单员订单{}的所有跟单订单处理完成", leaderOrder.getId());

        } catch (Exception e) {
            log.error("处理带单员{}的跟单订单失败", leaderOrder.getId(), e);
        }
    }
}
