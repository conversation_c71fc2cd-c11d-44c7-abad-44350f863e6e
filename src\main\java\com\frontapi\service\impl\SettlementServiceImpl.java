package com.frontapi.service.impl;

import com.frontapi.entity.DeliveryOrder;
import com.frontapi.mapper.DeliveryOrderMapper;
import com.frontapi.mapper.FrontUserMapper;
import com.frontapi.mapper.SysParamsMapper;
import com.frontapi.service.FollowRelationCleanupService;
import com.frontapi.mapper.TradeRecordMapper;
import com.frontapi.mapper.CommissionRecordMapper;
import com.frontapi.service.SettlementService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;


/**
 * 订单结算服务实现
 */
@Slf4j
@Service
public class SettlementServiceImpl implements SettlementService {

    @Autowired
    private FrontUserMapper frontUserMapper;
    
    @Autowired
    private SysParamsMapper sysParamsMapper;
    
    @Autowired
    private TradeRecordMapper tradeRecordMapper;
    
    @Autowired
    private CommissionRecordMapper commissionRecordMapper;

    @Autowired
    private FollowRelationCleanupService followRelationCleanupService;

    @Autowired
    private DeliveryOrderMapper deliveryOrderMapper;

    /**
     * 格式化保证金金额（保留2位小数）
     */
    private BigDecimal formatMarginAmount(BigDecimal marginAmount) {
        if (marginAmount == null) {
            return BigDecimal.ZERO;
        }
        return marginAmount.setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 格式化持仓数量（保留3位小数）
     */
    private BigDecimal formatPositionAmount(BigDecimal positionAmount) {
        if (positionAmount == null) {
            return BigDecimal.ZERO;
        }
        return positionAmount.setScale(3, RoundingMode.HALF_UP);
    }

    /**
     * 根据盈亏情况更新返佣状态
     * 盈利：rebate_status = 2 (已返)
     * 持平：rebate_status = 2 (已返)
     * 亏损：rebate_status = 1 (未返)
     */
    private void updateRebateStatusByProfit(DeliveryOrder order) {
        try {
            BigDecimal profit = order.getProfit();
            int rebateStatus;
            String statusDesc;

            if (profit != null && profit.compareTo(BigDecimal.ZERO) >= 0) {
                // 盈利或持平：设置为已返
                rebateStatus = 2;
                statusDesc = "已返";
                if (profit.compareTo(BigDecimal.ZERO) > 0) {
                    log.info("订单盈利，设置返佣状态为已返 - 订单ID: {}, 盈利: {}", order.getId(), profit);
                } else {
                    log.info("订单持平，设置返佣状态为已返 - 订单ID: {}, 盈利: {}", order.getId(), profit);
                }
            } else {
                // 亏损：设置为未返
                rebateStatus = 1;
                statusDesc = "未返";
                log.info("订单亏损，设置返佣状态为未返 - 订单ID: {}, 盈利: {}", order.getId(), profit);
            }

            int updateResult = deliveryOrderMapper.updateRebateStatus(order.getId(), rebateStatus);
            if (updateResult > 0) {
                log.info("更新订单返佣状态成功，订单ID: {}, 盈利: {}, 返佣状态: {}",
                        order.getId(), profit, statusDesc);
            } else {
                log.warn("更新订单返佣状态失败，订单ID: {}", order.getId());
            }

        } catch (Exception e) {
            log.error("更新订单返佣状态异常，订单ID: {}", order.getId(), e);
        }
    }

    /**
     * 批量更新相关订单的返佣状态（带单员订单触发）
     * 根据带单员订单的盈亏情况，更新所有相关订单的返佣状态
     * 盈利或持平：rebate_status = 2 (已返)
     * 亏损：rebate_status = 1 (未返)
     */
    private void updateRelatedOrdersRebateStatus(DeliveryOrder leaderOrder) {
        try {
            BigDecimal profit = leaderOrder.getProfit();
            int rebateStatus;
            String statusDesc;

            if (profit != null && profit.compareTo(BigDecimal.ZERO) >= 0) {
                // 盈利或持平：设置为已返
                rebateStatus = 2;
                statusDesc = "已返";
                if (profit.compareTo(BigDecimal.ZERO) > 0) {
                    log.info("带单员订单盈利，批量设置相关订单返佣状态为已返 - 订单ID: {}, 盈利: {}", leaderOrder.getId(), profit);
                } else {
                    log.info("带单员订单持平，批量设置相关订单返佣状态为已返 - 订单ID: {}, 盈利: {}", leaderOrder.getId(), profit);
                }
            } else {
                // 亏损：设置为未返
                rebateStatus = 1;
                statusDesc = "未返";
                log.info("带单员订单亏损，批量设置相关订单返佣状态为未返 - 订单ID: {}, 盈利: {}", leaderOrder.getId(), profit);
            }

            // 查询所有相关订单ID
            List<Long> relatedOrderIds = deliveryOrderMapper.getRelatedOrderIds(leaderOrder.getId());
            if (relatedOrderIds != null && !relatedOrderIds.isEmpty()) {
                // 批量更新返佣状态
                int updateResult = deliveryOrderMapper.batchUpdateRebateStatus(relatedOrderIds, rebateStatus);
                log.info("批量更新相关订单返佣状态完成，带单员订单ID: {}, 盈利: {}, 返佣状态: {}, 相关订单数: {}, 影响订单数: {}",
                        leaderOrder.getId(), profit, statusDesc, relatedOrderIds.size(), updateResult);
            } else {
                log.warn("未找到相关订单，带单员订单ID: {}", leaderOrder.getId());
            }

        } catch (Exception e) {
            log.error("批量更新相关订单返佣状态异常，带单员订单ID: {}", leaderOrder.getId(), e);
        }
    }

    /**
     * 更新带单员的返佣状态（当跟单订单盈利或持平时调用）
     */
    private void updateLeaderRebateStatusIfNeeded(Long leaderId, DeliveryOrder followOrder) {
        try {
            // 查询带单员在同一交易对的订单，且未返佣、未结算的
            List<DeliveryOrder> leaderOrders = deliveryOrderMapper.selectLeaderOrdersForRebateUpdate(
                leaderId, followOrder.getSymbol());

            if (!leaderOrders.isEmpty()) {
                for (DeliveryOrder leaderOrder : leaderOrders) {
                    // 检查带单员订单是否也是盈利或持平的
                    if (leaderOrder.getProfit() != null && leaderOrder.getProfit().compareTo(BigDecimal.ZERO) >= 0) {
                        int updateResult = deliveryOrderMapper.updateRebateStatus(leaderOrder.getId(), 2);
                        if (updateResult > 0) {
                            if (leaderOrder.getProfit().compareTo(BigDecimal.ZERO) > 0) {
                                log.info("跟单盈利时同步更新带单员返佣状态成功(盈利)，带单员订单ID: {}, 跟单订单ID: {}",
                                        leaderOrder.getId(), followOrder.getId());
                            } else {
                                log.info("跟单盈利时同步更新带单员返佣状态成功(持平)，带单员订单ID: {}, 跟单订单ID: {}",
                                        leaderOrder.getId(), followOrder.getId());
                            }
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.error("更新带单员返佣状态失败，带单员ID: {}, 跟单订单ID: {}", leaderId, followOrder.getId(), e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 更新订单结算状态
     */
    private void updateOrderSettlementStatus(Long orderId, int settlementStatus) {
        updateOrderSettlementStatus(orderId, settlementStatus, null);
    }

    /**
     * 更新订单结算状态和返佣状态
     */
    private void updateOrderSettlementStatus(Long orderId, int settlementStatus, Integer rebateStatus) {
        try {
            log.info("=== 开始更新订单结算状态 ===");
            if (rebateStatus != null) {
                log.info("订单ID: {}, 目标结算状态: {}, 目标返佣状态: {}", orderId, settlementStatus, rebateStatus);
            } else {
                log.info("订单ID: {}, 目标结算状态: {}", orderId, settlementStatus);
            }

            // 先查询当前状态
            com.frontapi.entity.DeliveryOrder currentOrder = deliveryOrderMapper.selectById(orderId);
            if (currentOrder != null) {
                log.info("更新前状态 - 订单ID: {}, 当前结算状态: {}, 当前返佣状态: {}, 订单状态: {}",
                        orderId, currentOrder.getIsSettlement(), currentOrder.getRebateStatus(), currentOrder.getStatus());
            } else {
                log.error("❌ 订单不存在，订单ID: {}", orderId);
                return;
            }

            // 根据是否需要更新返佣状态选择不同的更新方法
            int updateResult;
            if (rebateStatus != null) {
                updateResult = deliveryOrderMapper.updateSettlementAndRebateStatus(orderId, settlementStatus, rebateStatus);
                log.info("数据库更新操作结果（结算状态+返佣状态） - 影响行数: {}", updateResult);
            } else {
                updateResult = deliveryOrderMapper.updateSettlementStatus(orderId, settlementStatus);
                log.info("数据库更新操作结果（仅结算状态） - 影响行数: {}", updateResult);
            }

            if (updateResult > 0) {
                // 再次查询验证更新结果
                com.frontapi.entity.DeliveryOrder updatedOrder = deliveryOrderMapper.selectById(orderId);
                if (updatedOrder != null) {
                    if (rebateStatus != null) {
                        log.info("✅ 更新订单状态成功 - 订单ID: {}, 结算状态: {} → {}, 返佣状态: {} → {}",
                                orderId, currentOrder.getIsSettlement(), updatedOrder.getIsSettlement(),
                                currentOrder.getRebateStatus(), updatedOrder.getRebateStatus());

                        // 验证结算状态
                        if (updatedOrder.getIsSettlement() != settlementStatus) {
                            log.error("❌ 结算状态更新验证失败 - 订单ID: {}, 期望状态: {}, 实际状态: {}",
                                    orderId, settlementStatus, updatedOrder.getIsSettlement());
                            throw new RuntimeException("结算状态更新验证失败");
                        }

                        // 验证返佣状态
                        if (updatedOrder.getRebateStatus() != rebateStatus) {
                            log.error("❌ 返佣状态更新验证失败 - 订单ID: {}, 期望状态: {}, 实际状态: {}",
                                    orderId, rebateStatus, updatedOrder.getRebateStatus());
                            throw new RuntimeException("返佣状态更新验证失败");
                        }

                        log.info("🎯 结算状态和返佣状态更新验证成功 - 订单ID: {}, 结算状态: {}, 返佣状态: {}",
                                orderId, settlementStatus, rebateStatus);
                    } else {
                        log.info("✅ 更新订单结算状态成功 - 订单ID: {}, 结算状态: {} → {}",
                                orderId, currentOrder.getIsSettlement(), updatedOrder.getIsSettlement());

                        if (updatedOrder.getIsSettlement() != settlementStatus) {
                            log.error("❌ 状态更新验证失败 - 订单ID: {}, 期望状态: {}, 实际状态: {}",
                                    orderId, settlementStatus, updatedOrder.getIsSettlement());
                            throw new RuntimeException("结算状态更新验证失败");
                        } else {
                            log.info("🎯 结算状态更新验证成功 - 订单ID: {}, 状态: {}", orderId, settlementStatus);
                        }
                    }
                } else {
                    log.error("❌ 更新后查询订单失败，订单ID: {}", orderId);
                    throw new RuntimeException("更新后查询订单失败");
                }
            } else {
                String errorMsg = rebateStatus != null ?
                    String.format("❌ 更新订单状态失败 - 订单ID: %d, 结算状态: %d, 返佣状态: %d, 影响行数: %d",
                            orderId, settlementStatus, rebateStatus, updateResult) :
                    String.format("❌ 更新订单结算状态失败 - 订单ID: %d, 结算状态: %d, 影响行数: %d",
                            orderId, settlementStatus, updateResult);
                log.error(errorMsg);
                throw new RuntimeException("数据库更新操作失败，影响行数为0");
            }

            log.info("=== 订单结算状态更新完成 ===");

        } catch (Exception e) {
            log.error("更新订单结算状态异常，订单ID: {}, 结算状态: {}", orderId, settlementStatus, e);
            throw new RuntimeException("更新订单结算状态失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理跟单订单的资金结算
     */
    @Transactional(rollbackFor = Exception.class)
    public void processFollowOrderSettlement(DeliveryOrder followOrder) {
        try {
            log.info("=== 开始跟单订单结算 ===");
            log.info("订单ID: {}, 用户ID: {}, 带单员ID: {}",
                    followOrder.getId(), followOrder.getUserId(), followOrder.getLeaderId());
            log.info("订单盈利: {}, 盈利状态: {}", followOrder.getProfit(), followOrder.getProfitStatus());
            log.info("订单状态: {}, 结算状态: {}", followOrder.getStatus(), followOrder.getIsSettlement());

            // 获取系统参数
            BigDecimal copyTradeFee = sysParamsMapper.getCopyTradeFee(); // 手续费比例
            if (copyTradeFee == null) {
                copyTradeFee = new BigDecimal("10"); // 默认10%
            }

            BigDecimal platformFeeRate = sysParamsMapper.getPlatformFeeRate(); // 平台费率
            if (platformFeeRate == null) {
                platformFeeRate = new BigDecimal("50"); // 默认50%
            }

            BigDecimal profit = followOrder.getProfit();
            BigDecimal marginAmount = formatMarginAmount(followOrder.getMarginAmount());
            BigDecimal positionAmount = formatPositionAmount(followOrder.getPositionAmount());

            log.info("跟单订单结算数据格式化 - 订单ID: {}, 原始保证金: {}, 格式化保证金: {}, 原始持仓: {}, 格式化持仓: {}",
                    followOrder.getId(), followOrder.getMarginAmount(), marginAmount,
                    followOrder.getPositionAmount(), positionAmount);

            log.info("=== 判断盈亏情况 ===");
            log.info("订单ID: {}, 盈利金额: {}", followOrder.getId(), profit);
            log.info("盈利是否大于0: {}", profit.compareTo(BigDecimal.ZERO) > 0);

            if (profit.compareTo(BigDecimal.ZERO) > 0) {
                // 盈利情况
                log.info("✅ 进入盈利结算分支 - 订单ID: {}, 盈利: {}", followOrder.getId(), profit);
                processProfitSettlement(followOrder, copyTradeFee, platformFeeRate);
            } else {
                // 亏损情况
                log.info("❌ 进入亏损结算分支 - 订单ID: {}, 盈利: {}", followOrder.getId(), profit);
                processLossSettlement(followOrder, copyTradeFee);
            }

            // 根据盈亏情况确定返佣状态
            int rebateStatus = (profit.compareTo(BigDecimal.ZERO) >= 0) ? 2 : 1;  // 盈利或持平=2（已返），亏损=1（未返）

            // 更新订单结算状态和返佣状态为已结算
            updateOrderSettlementStatus(followOrder.getId(), 2, rebateStatus);

            // 同时更新带单员的返佣状态（如果是盈利或持平订单）
            if (profit.compareTo(BigDecimal.ZERO) >= 0) {
                updateLeaderRebateStatusIfNeeded(followOrder.getLeaderId(), followOrder);
            }

            log.info("跟单订单结算完成，订单ID: {}", followOrder.getId());

        } catch (Exception e) {
            log.error("跟单订单结算失败，订单ID: {}", followOrder.getId(), e);
            throw new RuntimeException("跟单订单结算失败", e);
        }
    }

    /**
     * 处理带单员订单的资金结算
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processLeaderOrderSettlement(DeliveryOrder leaderOrder) {
        try {
            log.info("开始处理带单员订单结算，订单ID: {}, 带单员ID: {}, 盈利: {}",
                    leaderOrder.getId(), leaderOrder.getLeaderId(), leaderOrder.getProfit());

            BigDecimal profit = leaderOrder.getProfit();
            BigDecimal marginAmount = formatMarginAmount(leaderOrder.getMarginAmount());
            BigDecimal positionAmount = formatPositionAmount(leaderOrder.getPositionAmount());
            Long leaderId = leaderOrder.getLeaderId();

            log.info("带单员订单结算数据格式化 - 订单ID: {}, 原始保证金: {}, 格式化保证金: {}, 原始持仓: {}, 格式化持仓: {}",
                    leaderOrder.getId(), leaderOrder.getMarginAmount(), marginAmount,
                    leaderOrder.getPositionAmount(), positionAmount);

            if (profit.compareTo(BigDecimal.ZERO) >= 0) {
                // 盈利或持平情况：和跟单员一样的结算逻辑
                log.info("带单员订单盈利或持平，进入盈利结算分支 - 订单ID: {}, 盈利: {}", leaderOrder.getId(), profit);
                processLeaderProfitSettlement(leaderOrder, leaderId, marginAmount, profit);
            } else {
                // 亏损情况：按亏损金额扣除保证金，并扣除手续费
                log.info("带单员订单亏损，进入亏损结算分支 - 订单ID: {}, 盈利: {}", leaderOrder.getId(), profit);
                processLeaderLossSettlement(leaderOrder, leaderId, marginAmount, profit);
            }

            // 先批量更新相关订单的返佣状态（包括带单员和所有跟单员）
            // 注意：必须在更新结算状态之前执行，因为查询条件会过滤已结算的订单
            updateRelatedOrdersRebateStatus(leaderOrder);

            log.info("带单员订单结算完成，跟单关系清除将在所有相关订单结算完成后统一处理，带单员ID: {}", leaderOrder.getLeaderId());

            // 根据盈亏情况确定返佣状态
            int rebateStatus = (profit.compareTo(BigDecimal.ZERO) >= 0) ? 2 : 1;  // 盈利或持平=2（已返），亏损=1（未返）

            // 最后更新订单结算状态和返佣状态为已结算（放在事务的最后）
            log.info("准备更新订单结算状态和返佣状态，订单ID: {}, 盈利: {}, 返佣状态: {}",
                    leaderOrder.getId(), profit, rebateStatus == 2 ? "已返利" : "未返利");
            updateOrderSettlementStatus(leaderOrder.getId(), 2, rebateStatus);  // 结算状态=2（已结算），返佣状态根据盈亏确定
            log.info("带单员订单结算完成，订单ID: {}", leaderOrder.getId());

        } catch (Exception e) {
            log.error("带单员订单结算失败，订单ID: {}", leaderOrder.getId(), e);
            throw new RuntimeException("带单员订单结算失败", e);
        }
    }

    /**
     * 处理带单员盈利结算（和跟单员相同的逻辑）
     */
    private void processLeaderProfitSettlement(DeliveryOrder leaderOrder, Long leaderId, BigDecimal marginAmount, BigDecimal profit) {
        try {
            log.info("带单员盈利结算开始 - 带单员ID: {}, 保证金: {}, 盈利: {}",
                    leaderId, marginAmount, profit);

            // 1. 返还保证金到带单员跟单账户
            returnMarginToLeader(leaderId, marginAmount, leaderOrder.getId());

            // 2. 获取手续费率和平台费率
            BigDecimal feeRate = sysParamsMapper.getCopyTradeFee(); // 手续费比例
            if (feeRate == null) {
                feeRate = new BigDecimal("10"); // 默认10%
            }

            BigDecimal platformFeeRate = sysParamsMapper.getPlatformFeeRate(); // 平台费率
            if (platformFeeRate == null) {
                platformFeeRate = new BigDecimal("50"); // 默认50%
            }

            // 3. 计算平仓手续费：成交数量 * 平仓价格 * 手续费率
            BigDecimal positionAmount = formatPositionAmount(leaderOrder.getPositionAmount()); // 成交数量
            BigDecimal closePrice = leaderOrder.getClosePrice(); // 平仓价格
            BigDecimal closeFee = positionAmount.multiply(closePrice).multiply(feeRate).divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP);

            // 4. 计算开仓手续费：成交数量 * 开仓价格 * 手续费率
            BigDecimal openPrice = leaderOrder.getOpenPrice(); // 开仓价格
            BigDecimal openFee = positionAmount.multiply(openPrice).multiply(feeRate).divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP);

            // 5. 计算总手续费和净利润
            BigDecimal totalFee = openFee.add(closeFee);
            BigDecimal netProfit = profit.subtract(totalFee); // 净利润 = 盈利 - 总手续费

            log.info("带单员手续费计算 - 成交数量: {}, 开仓价格: {}, 平仓价格: {}, 手续费率: {}%, 开仓手续费: {}, 平仓手续费: {}, 总手续费: {}, 总盈利: {}, 净利润: {}",
                    positionAmount, openPrice, closePrice, feeRate, openFee, closeFee, totalFee, profit, netProfit);

            // 6. 从带单员跟单账户扣除平仓手续费
            deductFeeFromUser(leaderId, closeFee, leaderOrder.getId());

            // 7. 判断原始盈利是否为正数
            if (profit.compareTo(BigDecimal.ZERO) > 0) {
                log.info("✅ 带单员原始盈利为正，开始分配收益 - 原始盈利: {}, 总手续费: {}, 净利润: {}",
                        profit, totalFee, netProfit);

                // 判断原始盈利是否大于总手续费
                if (profit.compareTo(totalFee) >= 0) {
                    // 原始盈利 >= 总手续费：使用净利润按50%分配
                    log.info("原始盈利大于等于总手续费，使用净利润按50%分配：50%给利润账户，50%给储备金");

                    BigDecimal leaderReserve = netProfit.multiply(platformFeeRate).divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP);
                    BigDecimal leaderProfit = netProfit.multiply(platformFeeRate).divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP);

                    log.info("带单员收益分配（50%分配模式） - 净利润: {}, 平台费率: {}%, 储备金: {}, 利润账户: {}",
                            netProfit, platformFeeRate, leaderReserve, leaderProfit);

                    // 增加带单员利润账户
                    addLeaderProfitBalance(leaderId, leaderProfit, leaderOrder.getId());

                    // 增加带单员储备金
                    addLeaderReserveAmount(leaderId, leaderReserve, leaderOrder.getId());

                    log.info("带单员盈利结算完成（50%分配模式） - 带单员ID: {}, 保证金返还: {}, 利润账户: {}, 储备金: {}",
                            leaderId, marginAmount, leaderProfit, leaderReserve);
                } else {
                    // 原始盈利 <= 总手续费：所有原始盈利都给用户
                    log.info("原始盈利小于等于总手续费，所有原始盈利都给用户 - 原始盈利: {}, 总手续费: {}", profit, totalFee);

                    BigDecimal leaderProfit = profit; // 所有原始盈利给用户
                    BigDecimal leaderReserve = BigDecimal.ZERO; // 储备金为0

                    log.info("带单员收益分配（全额给用户模式） - 原始盈利: {}, 利润账户: {}, 储备金: {}",
                            profit, leaderProfit, leaderReserve);

                    // 增加带单员利润账户（全部原始盈利）
                    addLeaderProfitBalance(leaderId, leaderProfit, leaderOrder.getId());

                    log.info("带单员盈利结算完成（全额给用户模式） - 带单员ID: {}, 保证金返还: {}, 利润账户: {}, 储备金: {}",
                            leaderId, marginAmount, leaderProfit, leaderReserve);
                }
            } else {
                log.warn("❌ 带单员原始盈利为负或零，跳过收益分配 - 带单员ID: {}, 原始盈利: {}",
                        leaderId, profit);
            }

            // 8. 处理佣金分配（给带单员的推荐人）
            processCommissionDistribution(leaderId, closeFee, leaderOrder.getId());

        } catch (Exception e) {
            log.error("带单员盈利结算失败，订单ID: {}", leaderOrder.getId(), e);
            throw new RuntimeException("带单员盈利结算失败", e);
        }
    }

    /**
     * 处理带单员亏损结算
     */
    private void processLeaderLossSettlement(DeliveryOrder leaderOrder, Long leaderId, BigDecimal marginAmount, BigDecimal profit) {
        BigDecimal loss = profit.abs(); // 亏损金额（正数）
        BigDecimal formattedMarginAmount = formatMarginAmount(marginAmount); // 格式化保证金

        if (loss.compareTo(formattedMarginAmount) >= 0) {
            // 亏损超过或等于保证金，保证金全部扣除
            log.info("带单员亏损超过保证金，订单ID: {}, 亏损: {}, 保证金: {}",
                    leaderOrder.getId(), loss, formattedMarginAmount);
        } else {
            // 亏损小于保证金，返还剩余部分
            BigDecimal remainingMargin = formattedMarginAmount.subtract(loss);
            returnMarginToLeader(leaderId, remainingMargin, leaderOrder.getId());
        }

        // 获取手续费率
        BigDecimal feeRate = sysParamsMapper.getCopyTradeFee(); // 手续费比例
        if (feeRate == null) {
            feeRate = new BigDecimal("10"); // 默认10%
        }

        // 计算手续费：成交数量 * 平仓价格 * 手续费率
        BigDecimal positionAmount = formatPositionAmount(leaderOrder.getPositionAmount()); // 成交数量
        BigDecimal closePrice = leaderOrder.getClosePrice(); // 平仓价格
        BigDecimal fee = positionAmount.multiply(closePrice).multiply(feeRate).divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP);

        log.info("带单员亏损手续费计算 - 成交数量: {}, 平仓价格: {}, 手续费率: {}%, 手续费: {}",
                positionAmount, closePrice, feeRate, fee);

        // 从带单员跟单账户扣除手续费
        deductFeeFromUser(leaderId, fee, leaderOrder.getId());

        // 处理佣金分配
        processCommissionDistribution(leaderId, fee, leaderOrder.getId());

        log.info("带单员亏损结算完成，订单ID: {}, 亏损: {}, 手续费: {}",
                leaderOrder.getId(), loss, fee);
    }

    /**
     * 返还保证金到带单员跟单账户
     */
    private void returnMarginToLeader(Long leaderId, BigDecimal amount, Long orderId) {
        // 增加带单员跟单账户余额
        int result = frontUserMapper.increaseCopyTradeBalance(leaderId, amount);
        if (result <= 0) {
            throw new RuntimeException("返还带单员保证金失败");
        }

        // 记录交易明细
        String orderIdStr = (orderId != null) ? orderId.toString() : "未知";
        addTradeRecord(leaderId, amount, "保证金返还", "带单员订单" + orderIdStr + "保证金返还", 2);

        log.info("返还带单员保证金成功，带单员ID: {}, 金额: {}, 订单ID: {}", leaderId, amount, orderId);
    }

    /**
     * 处理盈利情况的结算
     */
    private void processProfitSettlement(DeliveryOrder followOrder, BigDecimal feeRate, BigDecimal platformFeeRate) {
        BigDecimal profit = followOrder.getProfit();
        BigDecimal marginAmount = formatMarginAmount(followOrder.getMarginAmount());
        Long userId = followOrder.getUserId();
        Long leaderId = followOrder.getLeaderId();

        log.info("=== 跟单员盈利结算开始 ===");
        log.info("订单ID: {}, 用户ID: {}, 带单员ID: {}", followOrder.getId(), userId, leaderId);
        log.info("原始盈利: {}, 保证金: {}", profit, marginAmount);
        log.info("手续费率: {}%, 平台费率: {}%", feeRate, platformFeeRate);

        // 1. 返还保证金
        returnMarginToUser(userId, marginAmount, followOrder.getId());

        // 2. 计算平仓手续费：成交数量 * 平仓价格 * 手续费率
        BigDecimal positionAmount = formatPositionAmount(followOrder.getPositionAmount()); // 成交数量
        BigDecimal closePrice = followOrder.getClosePrice(); // 平仓价格
        BigDecimal closeFee = positionAmount.multiply(closePrice).multiply(feeRate).divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP);

        // 3. 计算开仓手续费：成交数量 * 开仓价格 * 手续费率
        BigDecimal openPrice = followOrder.getOpenPrice(); // 开仓价格
        BigDecimal openFee = positionAmount.multiply(openPrice).multiply(feeRate).divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP);

        // 4. 计算总手续费和净利润
        BigDecimal totalFee = openFee.add(closeFee);
        BigDecimal netProfit = profit.subtract(totalFee); // 净利润 = 盈利 - 总手续费

        log.info("手续费计算 - 成交数量: {}, 开仓价格: {}, 平仓价格: {}, 手续费率: {}%, 开仓手续费: {}, 平仓手续费: {}, 总手续费: {}, 总盈利: {}, 净盈利: {}",
                positionAmount, openPrice, closePrice, feeRate, openFee, closeFee, totalFee, profit, netProfit);

        // 5. 从跟单账户扣除平仓手续费
        deductFeeFromUser(userId, closeFee, followOrder.getId());

        log.info("=== 盈利计算结果 ===");
        log.info("原始盈利: {}, 总手续费: {}, 净利润: {}", profit, totalFee, netProfit);
        log.info("原始盈利是否大于0: {}", profit.compareTo(BigDecimal.ZERO) > 0);
        log.info("净利润是否大于等于0: {}", netProfit.compareTo(BigDecimal.ZERO) >= 0);

        if (profit.compareTo(BigDecimal.ZERO) > 0) {
            log.info("✅ 原始盈利为正，开始分配收益");

            // 判断原始盈利是否大于总手续费
            if (profit.compareTo(totalFee) >= 0) {
                // 原始盈利 >= 总手续费：使用净利润按50%分配
                log.info("原始盈利大于等于总手续费，使用净利润按50%分配：50%给用户，50%给带单员储备金");

                // 带单员储备金 = 净利润 × 平台费率 ÷ 100
                BigDecimal leaderReserve = netProfit.multiply(platformFeeRate).divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP);

                // 用户收益 = 净利润 × 平台费率 ÷ 100
                BigDecimal userProfit = netProfit.multiply(platformFeeRate).divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP);

                log.info("跟单员收益分配（50%分配模式） - 净利润: {}, 平台费率: {}%, 用户收益: {}, 带单员储备: {}",
                        netProfit, platformFeeRate, userProfit, leaderReserve);

                // 增加用户收益账户
                addUserProfitBalance(userId, userProfit, followOrder.getId());

                // 增加带单员储备金
                addLeaderReserveAmount(leaderId, leaderReserve, followOrder.getId());

                log.info("跟单员盈利结算完成（50%分配模式） - 用户ID: {}, 保证金返还: {}, 用户收益: {}, 带单员储备: {}",
                        userId, marginAmount, userProfit, leaderReserve);
            } else {
                // 原始盈利 <= 总手续费：所有原始盈利都给用户
                log.info("原始盈利小于等于总手续费，所有原始盈利都给用户 - 原始盈利: {}, 总手续费: {}", profit, totalFee);

                BigDecimal userProfit = profit; // 所有原始盈利给用户
                BigDecimal leaderReserve = BigDecimal.ZERO; // 带单员储备为0

                log.info("跟单员收益分配（全额给用户模式） - 原始盈利: {}, 用户收益: {}, 带单员储备: {}",
                        profit, userProfit, leaderReserve);

                // 增加用户收益账户（全部原始盈利）
                addUserProfitBalance(userId, userProfit, followOrder.getId());

                log.info("跟单员盈利结算完成（全额给用户模式） - 用户ID: {}, 保证金返还: {}, 用户收益: {}, 带单员储备: {}",
                        userId, marginAmount, userProfit, leaderReserve);
            }
        } else {
            log.warn("❌ 原始盈利为负或零，跳过收益分配");
            log.warn("用户ID: {}, 原始盈利: {}, 总手续费: {}, 净利润: {}", userId, profit, totalFee, netProfit);
            log.warn("收益分配被跳过，用户不会获得收益");
        }

        // 7. 处理佣金分配（使用平仓手续费）
        processCommissionDistribution(userId, closeFee, followOrder.getId());

        log.info("盈利结算完成，订单ID: {}, 总盈利: {}, 平仓手续费: {}, 净盈利: {}",
                followOrder.getId(), profit, closeFee, netProfit);
    }

    /**
     * 处理亏损情况的结算
     */
    private void processLossSettlement(DeliveryOrder followOrder, BigDecimal feeRate) {
        BigDecimal loss = followOrder.getProfit().abs(); // 亏损金额（正数）
        BigDecimal marginAmount = formatMarginAmount(followOrder.getMarginAmount());
        Long userId = followOrder.getUserId();

        if (loss.compareTo(marginAmount) >= 0) {
            // 亏损超过或等于保证金，保证金全部扣除，无需返还
            log.info("亏损超过保证金，订单ID: {}, 亏损: {}, 保证金: {}",
                    followOrder.getId(), loss, marginAmount);
        } else {
            // 亏损小于保证金，返还剩余部分
            BigDecimal remainingMargin = marginAmount.subtract(loss);
            returnMarginToUser(userId, remainingMargin, followOrder.getId());
        }

        // 计算手续费：成交数量 * 平仓价格 * 手续费率
        BigDecimal positionAmount = formatPositionAmount(followOrder.getPositionAmount()); // 成交数量
        BigDecimal closePrice = followOrder.getClosePrice(); // 平仓价格
        BigDecimal fee = positionAmount.multiply(closePrice).multiply(feeRate).divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP);

        log.info("亏损手续费计算 - 成交数量: {}, 平仓价格: {}, 手续费率: {}%, 手续费: {}",
                positionAmount, closePrice, feeRate, fee);

        // 从跟单账户扣除手续费
        deductFeeFromUser(userId, fee, followOrder.getId());

        // 处理佣金分配
        processCommissionDistribution(userId, fee, followOrder.getId());

        log.info("亏损结算完成，订单ID: {}, 亏损: {}, 手续费: {}",
                followOrder.getId(), loss, fee);
    }

    /**
     * 返还保证金到用户跟单账户
     */
    private void returnMarginToUser(Long userId, BigDecimal amount, Long orderId) {
        log.info("开始返还保证金 - 用户ID: {}, 金额: {}, 订单ID: {}", userId, amount, orderId);

        // 检查金额是否有效
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("保证金金额无效，跳过处理 - 用户ID: {}, 金额: {}", userId, amount);
            return;
        }

        try {
            // 增加用户跟单账户余额
            int result = frontUserMapper.increaseCopyTradeBalance(userId, amount);
            log.info("保证金返还数据库更新结果 - 用户ID: {}, 影响行数: {}", userId, result);

            if (result <= 0) {
                log.error("返还保证金失败 - 用户ID: {}, 金额: {}, 影响行数: {}", userId, amount, result);
                throw new RuntimeException("返还保证金失败，用户ID: " + userId);
            }

            // 记录交易明细
            String orderIdStr = (orderId != null) ? orderId.toString() : "未知";
            String remark = "订单" + orderIdStr + "保证金返还";
            log.info("开始记录保证金返还明细 - 用户ID: {}, 金额: {}, 备注: {}", userId, amount, remark);

            addTradeRecord(userId, amount, "保证金返还", remark, 2);

            log.info("返还保证金成功 - 用户ID: {}, 金额: {}, 订单ID: {}", userId, amount, orderId);

        } catch (Exception e) {
            log.error("返还保证金异常 - 用户ID: {}, 金额: {}, 订单ID: {}", userId, amount, orderId, e);
            throw new RuntimeException("返还保证金失败: " + e.getMessage(), e);
        }
    }

    /**
     * 增加用户收益账户余额
     */
    private void addUserProfitBalance(Long userId, BigDecimal amount, Long orderId) {
        log.info("=== 开始增加用户收益账户 ===");
        log.info("用户ID: {}, 收益金额: {}, 订单ID: {}", userId, amount, orderId);

        // 检查用户是否存在
        try {
            com.frontapi.entity.FrontUser user = frontUserMapper.selectById(userId);
            if (user != null) {
                log.info("用户存在，当前收益余额: {}", user.getProfitBalance());
            } else {
                log.error("❌ 用户不存在，用户ID: {}", userId);
                throw new RuntimeException("用户不存在，用户ID: " + userId);
            }
        } catch (Exception e) {
            log.error("检查用户存在性失败，用户ID: {}", userId, e);
            throw new RuntimeException("检查用户失败: " + e.getMessage(), e);
        }

        // 检查金额是否有效
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("❌ 收益金额无效，跳过处理 - 用户ID: {}, 金额: {}", userId, amount);
            return;
        }

        try {
            // 增加用户收益账户余额
            log.info("执行数据库更新 - UPDATE front_user SET profit_balance = profit_balance + {} WHERE id = {}", amount, userId);
            int result = frontUserMapper.increaseProfitBalance(userId, amount);
            log.info("数据库更新结果 - 用户ID: {}, 影响行数: {}", userId, result);

            if (result <= 0) {
                log.error("❌ 增加用户收益账户失败 - 用户ID: {}, 金额: {}, 影响行数: {}", userId, amount, result);
                throw new RuntimeException("增加用户收益账户失败，用户ID: " + userId);
            }

            log.info("✅ 数据库更新成功，影响行数: {}", result);

            // 验证更新后的余额
            try {
                com.frontapi.entity.FrontUser updatedUser = frontUserMapper.selectById(userId);
                if (updatedUser != null) {
                    log.info("更新后收益余额: {}", updatedUser.getProfitBalance());
                } else {
                    log.warn("验证时用户不存在: {}", userId);
                }
            } catch (Exception e) {
                log.warn("验证更新后余额失败: {}", e.getMessage());
            }

            // 记录收益明细
            String orderIdStr = (orderId != null) ? orderId.toString() : "未知";
            String remark = "订单" + orderIdStr + "跟单收益";
            log.info("开始记录收益明细 - 用户ID: {}, 金额: {}, 备注: {}", userId, amount, remark);

            addCommissionRecord(userId, amount, 2, remark);

            log.info("✅ 增加用户收益成功 - 用户ID: {}, 金额: {}, 订单ID: {}", userId, amount, orderId);

        } catch (Exception e) {
            log.error("增加用户收益账户异常 - 用户ID: {}, 金额: {}, 订单ID: {}", userId, amount, orderId, e);
            throw new RuntimeException("增加用户收益账户失败: " + e.getMessage(), e);
        }
    }

    /**
     * 增加带单员利润账户余额
     */
    private void addLeaderProfitBalance(Long leaderId, BigDecimal amount, Long orderId) {
        log.info("=== 开始增加带单员利润账户 ===");
        log.info("带单员ID: {}, 利润金额: {}, 订单ID: {}", leaderId, amount, orderId);

        // 检查金额是否有效
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("❌ 利润金额无效，跳过处理 - 带单员ID: {}, 金额: {}", leaderId, amount);
            return;
        }

        try {
            // 增加带单员利润账户余额
            int result = frontUserMapper.increaseProfitBalance(leaderId, amount);
            if (result <= 0) {
                log.error("❌ 增加带单员利润账户失败 - 带单员ID: {}, 金额: {}, 影响行数: {}", leaderId, amount, result);
                throw new RuntimeException("增加带单员利润账户失败，带单员ID: " + leaderId);
            }

            log.info("✅ 带单员利润账户更新成功，影响行数: {}", result);

            // 记录收益明细
            String orderIdStr = (orderId != null) ? orderId.toString() : "未知";
            String remark = "订单" + orderIdStr + "带单员利润收益";
            log.info("开始记录带单员利润明细 - 带单员ID: {}, 金额: {}, 备注: {}", leaderId, amount, remark);

            addCommissionRecord(leaderId, amount, 2, remark);

            log.info("✅ 增加带单员利润账户成功 - 带单员ID: {}, 金额: {}, 订单ID: {}", leaderId, amount, orderId);

        } catch (Exception e) {
            log.error("增加带单员利润账户异常 - 带单员ID: {}, 金额: {}, 订单ID: {}", leaderId, amount, orderId, e);
            throw new RuntimeException("增加带单员利润账户失败: " + e.getMessage(), e);
        }
    }

    /**
     * 增加带单员储备金
     */
    private void addLeaderReserveAmount(Long leaderId, BigDecimal amount, Long orderId) {
        // 增加带单员储备金
        int result = frontUserMapper.increaseReserveAmount(leaderId, amount);
        if (result <= 0) {
            throw new RuntimeException("增加带单员储备金失败");
        }

        log.info("增加带单员储备金成功，带单员ID: {}, 金额: {}, 订单ID: {}", leaderId, amount, orderId);
    }

    /**
     * 从用户跟单账户扣除手续费
     */
    private void deductFeeFromUser(Long userId, BigDecimal fee, Long orderId) {
        // 扣除用户跟单账户余额
        int result = frontUserMapper.decreaseCopyTradeBalance(userId, fee);
        if (result <= 0) {
            throw new RuntimeException("扣除手续费失败");
        }

        // 记录交易明细
        String orderIdStr = (orderId != null) ? orderId.toString() : "未知";
        addTradeRecord(userId, fee.negate(), "手续费扣除", "订单" + orderIdStr + "手续费", 2);
        
        log.info("扣除手续费成功，用户ID: {}, 金额: {}, 订单ID: {}", userId, fee, orderId);
    }

    /**
     * 记录交易明细
     */
    private void addTradeRecord(Long userId, BigDecimal amount, String tradeType, String remark, int accountType) {
        log.info("开始记录交易明细 - 用户ID: {}, 金额: {}, 类型: {}, 账户类型: {}", userId, amount, tradeType, accountType);

        try {
            // 获取用户信息
            String username = frontUserMapper.getUsernameById(userId);
            if (username == null) {
                username = "用户" + userId;
            }

            // 创建交易记录
            com.frontapi.entity.TradeRecord record = new com.frontapi.entity.TradeRecord();
            record.setUserId(userId);
            record.setUsername(username);
            record.setTradeType(tradeType);
            record.setAmount(amount);
            record.setAccountType(accountType);
            record.setRemark(remark);
            record.setCreateTime(java.time.LocalDateTime.now());
            record.setUpdateTime(java.time.LocalDateTime.now());

            int result = tradeRecordMapper.insert(record);
            if (result <= 0) {
                log.error("记录交易明细失败 - 用户ID: {}, 金额: {}, 类型: {}, 影响行数: {}",
                        userId, amount, tradeType, result);
                throw new RuntimeException("记录交易明细失败，用户ID: " + userId);
            } else {
                log.info("记录交易明细成功 - 用户ID: {}, 金额: {}, 类型: {}, 备注: {}",
                        userId, amount, tradeType, remark);
            }
        } catch (Exception e) {
            log.error("记录交易明细异常 - 用户ID: {}, 金额: {}, 类型: {}", userId, amount, tradeType, e);
            throw new RuntimeException("记录交易明细失败: " + e.getMessage(), e);
        }
    }

    /**
     * 记录收益明细
     */
    private void addCommissionRecord(Long userId, BigDecimal amount, int commissionType, String remark) {
        log.info("=== 开始记录收益明细 ===");
        log.info("用户ID: {}, 金额: {}, 类型: {} (2=收益), 备注: {}", userId, amount, commissionType, remark);

        // 获取用户信息
        String email = frontUserMapper.getEmailById(userId);
        log.info("获取用户邮箱: {}", email);
            
        if (email == null || email.trim().isEmpty()) {
            email = "user" + userId + "@example.com";
        }

        String username = frontUserMapper.getUsernameById(userId);
        if (username == null || username.trim().isEmpty()) {
            username = "用户" + userId;
        }

        // 确保 remark 不为 null
        if (remark == null) {
            remark = "佣金发放";
        }

        // 创建收益记录
        com.frontapi.entity.CommissionRecord record = new com.frontapi.entity.CommissionRecord();
        record.setUserId(userId);
        record.setUsername(username);
        record.setPhone(email); // 在phone字段中存储邮件值，确保不为null
        record.setCommissionType(commissionType);
        record.setCommissionAmount(amount); // 使用正确的字段名
        record.setRemark(remark);
        record.setReleaseStatus(1); // 1表示已赠送
        record.setReleaseTime(java.time.LocalDateTime.now()); // 设置赠送时间
        record.setCreateTime(java.time.LocalDateTime.now());
        record.setUpdateTime(java.time.LocalDateTime.now());

        try {
            log.info("执行数据库插入 - commission_record 表");
            int result = commissionRecordMapper.insert(record);
            log.info("数据库插入结果 - 影响行数: {}", result);

            if (result <= 0) {
                log.error("❌ 记录收益明细失败 - 用户ID: {}, 金额: {}, 类型: {}, 影响行数: {}",
                        userId, amount, commissionType, result);
                throw new RuntimeException("记录收益明细失败，用户ID: " + userId);
            } else {
                log.info("✅ 记录收益明细成功 - 用户ID: {}, 金额: {}, 类型: {}, 备注: {}",
                        userId, amount, commissionType, remark);
                log.info("明细记录ID: {}", record.getId());
            }
        } catch (Exception e) {
            log.error("❌ 记录收益明细异常 - 用户ID: {}, 金额: {}, 类型: {}", userId, amount, commissionType, e);
            throw new RuntimeException("记录收益明细失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理佣金分配（级差模式）
     * 级差规则：上级佣金比例 - 已发佣金比例 = 上级实际获得的佣金比例
     * 如果没有一键跟单则跳过，继续向上查找
     */
    private void processCommissionDistribution(Long userId, BigDecimal totalCommission, Long orderId) {
        log.info("开始处理佣金分配（级差模式），用户ID: {}, 总佣金: {}, 订单ID: {}", userId, totalCommission, orderId);

        if (totalCommission.compareTo(BigDecimal.ZERO) <= 0) {
            log.info("佣金金额为0或负数，跳过佣金分配");
            return;
        }

        try {
            // 获取用户推荐关系信息
            com.frontapi.vo.UserReferralVO userInfo = frontUserMapper.getUserReferralInfo(userId);
            if (userInfo == null || userInfo.getReferrerCode() == null) {
                log.info("用户{}没有推荐人，佣金分配结束", userId);
                return;
            }

            // 开始向上查找推荐链（级差模式）
            BigDecimal alreadyDistributedRate = BigDecimal.ZERO; // 已发放的佣金比例
            String currentReferrerCode = userInfo.getReferrerCode();
            int level = 1; // 推荐层级
            final int maxLevel = 10; // 最大查找层级，防止无限循环

            log.info("=== 开始级差佣金分配 ===");
            log.info("初始已发放佣金比例: {}", alreadyDistributedRate);

            while (currentReferrerCode != null && level <= maxLevel) {
                // 根据推荐码查找推荐人信息
                com.frontapi.vo.UserReferralVO referrerInfo = frontUserMapper.getUserByShareCode(currentReferrerCode);
                if (referrerInfo == null) {
                    log.info("推荐码{}对应的用户不存在，佣金分配结束", currentReferrerCode);
                    break;
                }

                Long referrerId = referrerInfo.getUserId();
                BigDecimal commissionRate = referrerInfo.getCommissionRate();
                Integer isCopyTrade = referrerInfo.getIsCopyTrade();

                log.info("第{}层推荐人: ID={}, 佣金比例={}, 一键跟单={}, 已发放比例={}",
                        level, referrerId, commissionRate, isCopyTrade, alreadyDistributedRate);

                // 检查是否有一键跟单
                if (isCopyTrade != null && isCopyTrade == 1) {
                    // 有一键跟单，检查佣金比例
                    if (commissionRate != null && commissionRate.compareTo(BigDecimal.ZERO) > 0) {
                        // 计算级差佣金比例：当前层级佣金比例 - 已发放佣金比例
                        BigDecimal differentialRate = commissionRate.subtract(alreadyDistributedRate);

                        log.info("级差计算 - 当前层级比例: {}, 已发放比例: {}, 级差比例: {}",
                                commissionRate, alreadyDistributedRate, differentialRate);

                        if (differentialRate.compareTo(BigDecimal.ZERO) > 0) {
                            // 级差为正，发放佣金
                            BigDecimal commissionAmount = totalCommission.multiply(differentialRate)
                                    .divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP);

                            if (commissionAmount.compareTo(BigDecimal.ZERO) > 0) {
                                distributeCommissionToUser(referrerId, commissionAmount, orderId, level);
                                log.info("✅ 第{}层推荐人获得级差佣金: {}% ({})", level, differentialRate, commissionAmount);
                            }

                            // 更新已发放佣金比例为当前层级的佣金比例
                            alreadyDistributedRate = commissionRate;
                        } else {
                            log.info("⚠️ 第{}层推荐人级差为负或零，跳过发放: {}%", level, differentialRate);
                        }
                    } else {
                        log.info("第{}层推荐人有一键跟单但无佣金比例，跳过", level);
                    }
                } else {
                    log.info("第{}层推荐人无一键跟单，跳过发放，继续向上查找", level);
                }

                // 继续向上查找
                currentReferrerCode = referrerInfo.getReferrerCode();
                level++;
            }

            log.info("=== 级差佣金分配完成 ===");
            log.info("最终已发放佣金比例: {}", alreadyDistributedRate);

        } catch (Exception e) {
            log.error("佣金分配过程中出现异常，用户ID: {}, 订单ID: {}", userId, orderId, e);
        }
    }

    /**
     * 向指定用户发放佣金
     */
    private void distributeCommissionToUser(Long userId, BigDecimal amount, Long orderId, int level) {
        try {
            // 增加用户佣金账户余额
            int result = frontUserMapper.increaseCommissionBalance(userId, amount);
            if (result <= 0) {
                log.error("增加用户{}佣金余额失败", userId);
                return;
            }

            // 记录佣金明细
            String orderIdStr = (orderId != null) ? orderId.toString() : "未知";
            addCommissionRecord(userId, amount, 1,
                    "订单" + orderIdStr + "第" + level + "层推荐佣金");

            log.info("成功发放佣金给用户{}, 金额: {}, 层级: {}, 订单ID: {}",
                    userId, amount, level, orderId);

        } catch (Exception e) {
            log.error("发放佣金失败，用户ID: {}, 金额: {}, 订单ID: {}", userId, amount, orderId, e);
        }
    }

    /**
     * 处理开仓手续费扣除和佣金分配
     * 在订单创建完成后调用，扣除开仓手续费并计算佣金分配
     * @param order 订单信息
     */
    public void processOpenCommissionDistribution(DeliveryOrder order) {
        try {
            log.info("开始处理开仓手续费扣除和佣金分配，订单ID: {}, 用户ID: {}", order.getId(), order.getUserId());

            // 获取手续费率
            BigDecimal feeRate = sysParamsMapper.getCopyTradeFee(); // 手续费比例
            if (feeRate == null) {
                feeRate = new BigDecimal("10"); // 默认10%
            }

            // 计算开仓手续费：成交数量 * 开仓价格 * 手续费率
            BigDecimal positionAmount = formatPositionAmount(order.getPositionAmount()); // 成交数量
            BigDecimal openPrice = order.getOpenPrice(); // 开仓价格
            BigDecimal openFee = positionAmount.multiply(openPrice).multiply(feeRate).divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP);

            log.info("开仓手续费计算 - 订单ID: {}, 原始成交数量: {}, 格式化成交数量: {}, 开仓价格: {}, 手续费率: {}%, 开仓手续费: {}",
                    order.getId(), order.getPositionAmount(), positionAmount, openPrice, feeRate, openFee);

            if (openFee.compareTo(BigDecimal.ZERO) > 0) {
                // 1. 从跟单账户扣除开仓手续费
                deductFeeFromUser(order.getUserId(), openFee, order.getId());

                // 2. 使用开仓手续费进行佣金分配
                try {
                    processCommissionDistribution(order.getUserId(), openFee, order.getId());
                    log.info("开仓手续费扣除和佣金分配完成，订单ID: {}, 手续费: {}", order.getId(), openFee);
                } catch (Exception e) {
                    log.error("佣金分配失败，但继续处理，订单ID: {}, 错误: {}", order.getId(), e.getMessage());
                    // 佣金分配失败不影响订单状态更新
                }
            } else {
                log.info("开仓手续费为0，跳过扣除和佣金分配，订单ID: {}", order.getId());
            }

            // 3. 佣金发放完成后，将订单状态从开仓处理中(0)更新为持仓中(1)
            // 无论佣金分配是否成功，都要更新订单状态
            int updateResult = deliveryOrderMapper.updateStatusToOpen(order.getId());
            if (updateResult > 0) {
                log.info("订单状态更新成功，订单ID: {} 从开仓处理中更新为持仓中", order.getId());
            } else {
                log.warn("订单状态更新失败，订单ID: {}, 可能已被其他操作更新", order.getId());
            }

        } catch (Exception e) {
            log.error("开仓手续费扣除失败，订单ID: {}", order.getId(), e);
            // 手续费扣除失败应该影响订单创建，但先尝试更新状态
            try {
                deliveryOrderMapper.updateStatusToOpen(order.getId());
                log.info("异常情况下仍成功更新订单状态，订单ID: {}", order.getId());
            } catch (Exception statusUpdateException) {
                log.error("异常情况下订单状态更新也失败，订单ID: {}", order.getId(), statusUpdateException);
            }
            throw new RuntimeException("开仓手续费扣除失败，订单ID: " + order.getId(), e);
        }
    }
}
