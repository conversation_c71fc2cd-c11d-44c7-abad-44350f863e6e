package com.frontapi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.frontapi.dto.TransferDTO;
import com.frontapi.entity.FrontUser;
import com.frontapi.entity.TransferRecord;
import com.frontapi.entity.SysParams;
import com.frontapi.exception.BusinessException;
import com.frontapi.mapper.FrontUserMapper;
import com.frontapi.mapper.TransferRecordMapper;
import com.frontapi.mapper.SysParamsMapper;
import com.frontapi.mapper.UserMapper;
import com.frontapi.service.TransferService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;

@Service
@RequiredArgsConstructor
public class TransferServiceImpl implements TransferService {

    private final FrontUserMapper frontUserMapper;
    private final UserMapper userMapper;
    private final TransferRecordMapper transferRecordMapper;
    private final SysParamsMapper sysParamsMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createTransfer(Long userId, TransferDTO transferDTO) {
        // 1. 获取系统转账参数
        SysParams params = sysParamsMapper.selectOne(new QueryWrapper<>());
        if (params == null) {
            throw new BusinessException("系统参数未配置");
        }

        // 2. 检查是否允许转账
        if (!params.getEnableTransfer()) {
            throw new BusinessException("当前系统暂停转账功能");
        }

        // 3. 检查转账金额限制
        BigDecimal amount = transferDTO.getAmount();
        if (amount.compareTo(params.getMinTransfer()) < 0) {
            throw new BusinessException("转账金额不能小于" + params.getMinTransfer() + "USDT");
        }
        if (amount.compareTo(params.getMaxTransfer()) > 0) {
            throw new BusinessException("转账金额不能大于" + params.getMaxTransfer() + "USDT");
        }

        // 4. 获取转出用户信息
        FrontUser fromUser = frontUserMapper.selectById(userId);
        if (fromUser == null) {
            throw new BusinessException("用户不存在");
        }
        
        // 5. 获取接收用户信息
        FrontUser toUser = null;
        if (transferDTO.getToUid() != null && !transferDTO.getToUid().isEmpty()) {
            toUser = userMapper.selectByUserNo(transferDTO.getToUid());
        } else if (transferDTO.getToEmail() != null && !transferDTO.getToEmail().isEmpty()) {
            toUser = userMapper.selectByEmail(transferDTO.getToEmail());
        }  
        if (toUser == null) {
            throw new BusinessException("接收用户不存在");
        }
        
        // 6. 不能给自己转账
        if (fromUser.getId().equals(toUser.getId())) {
            throw new BusinessException("不能给自己转账");
        }

        // 7. 校验转账金额
        if (fromUser.getAvailableBalance().compareTo(amount) < 0) {
            throw new BusinessException("可用余额不足");
        }

        // 8. 计算手续费和实际到账金额
        // BigDecimal feeRate = params.getTransferFee().divide(new BigDecimal("100")); // 将百分比转换为小数
        BigDecimal fee = params.getTransferFee();
        BigDecimal realAmount = amount.subtract(fee);
        
        // 9. 扣除转出方余额（使用SQL命令，只修改相关字段）
        int deductResult = frontUserMapper.decreaseAvailableBalanceForTransfer(fromUser.getId(), amount);
        if (deductResult <= 0) {
            throw new RuntimeException("扣除转出方余额失败");
        }

        // 10. 增加接收方余额（使用SQL命令，只修改相关字段）
        int addResult = frontUserMapper.increaseAvailableBalanceForTransfer(toUser.getId(), realAmount);
        if (addResult <= 0) {
            throw new RuntimeException("增加接收方余额失败");
        }

        // 11. 创建转账记录
        TransferRecord record = new TransferRecord();
        record.setFromUserId(fromUser.getId());
        record.setFromUsername(fromUser.getUsername() == null ? fromUser.getEmail() : fromUser.getUsername());
        record.setFromUserNo(fromUser.getUserNo());
        record.setToUserId(toUser.getId());
        record.setToUsername(toUser.getUsername() == null ? toUser.getEmail() : toUser.getUsername());
        record.setToUserNo(toUser.getUserNo());
        record.setAmount(amount);
        record.setFee(fee);
        record.setRealAmount(realAmount);
        record.setStatus(1); // 成功状态
        LocalDateTime now = LocalDateTime.now();
        record.setUpdateTime(now);
        record.setCreateTime(now);
        
        transferRecordMapper.insert(record);
    }
} 